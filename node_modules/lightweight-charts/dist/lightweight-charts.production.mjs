/*!
 * @license
 * TradingView Lightweight Charts™ v5.0.8
 * Copyright (c) 2025 TradingView, Inc.
 * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
 */
import{size as t,bindCanvasElementBitmapSizeTo as i,equalSizes as s,tryCreateCanvasRenderingTarget2D as n}from"fancy-canvas";const e={title:"",visible:!0,lastValueVisible:!0,priceLineVisible:!0,priceLineSource:0,priceLineWidth:1,priceLineColor:"",priceLineStyle:2,baseLineVisible:!0,baseLineWidth:1,baseLineColor:"#B2B5BE",baseLineStyle:0,priceFormat:{type:"price",precision:2,minMove:.01}};var r,h;function a(t,i){const s={0:[],1:[t.lineWidth,t.lineWidth],2:[2*t.lineWidth,2*t.lineWidth],3:[6*t.lineWidth,6*t.lineWidth],4:[t.lineWidth,4*t.lineWidth]}[i];t.setLineDash(s)}function l(t,i,s,n){t.beginPath();const e=t.lineWidth%2?.5:0;t.moveTo(s,i+e),t.lineTo(n,i+e),t.stroke()}function o(t,i){if(!t)throw new Error("Assertion failed"+(i?": "+i:""))}function _(t){if(void 0===t)throw new Error("Value is undefined");return t}function u(t){if(null===t)throw new Error("Value is null");return t}function c(t){return u(_(t))}!function(t){t[t.Simple=0]="Simple",t[t.WithSteps=1]="WithSteps",t[t.Curved=2]="Curved"}(r||(r={})),function(t){t[t.Solid=0]="Solid",t[t.Dotted=1]="Dotted",t[t.Dashed=2]="Dashed",t[t.LargeDashed=3]="LargeDashed",t[t.SparseDotted=4]="SparseDotted"}(h||(h={}));class d{constructor(){this.t=[]}i(t,i,s){const n={h:t,l:i,o:!0===s};this.t.push(n)}_(t){const i=this.t.findIndex((i=>t===i.h));i>-1&&this.t.splice(i,1)}u(t){this.t=this.t.filter((i=>i.l!==t))}p(t,i,s){const n=[...this.t];this.t=this.t.filter((t=>!t.o)),n.forEach((n=>n.h(t,i,s)))}v(){return this.t.length>0}m(){this.t=[]}}function f(t,...i){for(const s of i)for(const i in s)void 0!==s[i]&&Object.prototype.hasOwnProperty.call(s,i)&&!["__proto__","constructor","prototype"].includes(i)&&("object"!=typeof s[i]||void 0===t[i]||Array.isArray(s[i])?t[i]=s[i]:f(t[i],s[i]));return t}function p(t){return"number"==typeof t&&isFinite(t)}function v(t){return"number"==typeof t&&t%1==0}function m(t){return"string"==typeof t}function w(t){return"boolean"==typeof t}function g(t){const i=t;if(!i||"object"!=typeof i)return i;let s,n,e;for(n in s=Array.isArray(i)?[]:{},i)i.hasOwnProperty(n)&&(e=i[n],s[n]=e&&"object"==typeof e?g(e):e);return s}function M(t){return null!==t}function b(t){return null===t?void 0:t}const S="-apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif";function x(t,i,s){return void 0===i&&(i=S),`${s=void 0!==s?`${s} `:""}${t}px ${i}`}class C{constructor(t){this.M={S:1,C:5,P:NaN,k:"",T:"",R:"",D:"",V:0,I:0,B:0,A:0,L:0},this.O=t}N(){const t=this.M,i=this.F(),s=this.W();return t.P===i&&t.T===s||(t.P=i,t.T=s,t.k=x(i,s),t.A=2.5/12*i,t.V=t.A,t.I=i/12*t.C,t.B=i/12*t.C,t.L=0),t.R=this.H(),t.D=this.U(),this.M}H(){return this.O.N().layout.textColor}U(){return this.O.$()}F(){return this.O.N().layout.fontSize}W(){return this.O.N().layout.fontFamily}}function P(t){return t<0?0:t>255?255:Math.round(t)||0}function k(t){return.199*t[0]+.687*t[1]+.114*t[2]}class y{constructor(t,i){this.q=new Map,this.Y=t,i&&(this.q=i)}j(t,i){if("transparent"===t)return t;const s=this.K(t),n=s[3];return`rgba(${s[0]}, ${s[1]}, ${s[2]}, ${i*n})`}X(t){const i=this.K(t);return{Z:`rgb(${i[0]}, ${i[1]}, ${i[2]})`,G:k(i)>160?"black":"white"}}J(t){return k(this.K(t))}tt(t,i,s){const[n,e,r,h]=this.K(t),[a,l,o,_]=this.K(i),u=[P(n+s*(a-n)),P(e+s*(l-e)),P(r+s*(o-r)),(c=h+s*(_-h),c<=0||c>1?Math.min(Math.max(c,0),1):Math.round(1e4*c)/1e4)];var c;return`rgba(${u[0]}, ${u[1]}, ${u[2]}, ${u[3]})`}K(t){const i=this.q.get(t);if(i)return i;const s=function(t){const i=document.createElement("div");i.style.display="none",document.body.appendChild(i),i.style.color=t;const s=window.getComputedStyle(i).color;return document.body.removeChild(i),s}(t),n=s.match(/^rgba?\s*\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/);if(!n){if(this.Y.length)for(const i of this.Y){const s=i(t);if(s)return this.q.set(t,s),s}throw new Error(`Failed to parse color: ${t}`)}const e=[parseInt(n[1],10),parseInt(n[2],10),parseInt(n[3],10),n[4]?parseFloat(n[4]):1];return this.q.set(t,e),e}}class T{constructor(){this.it=[]}st(t){this.it=t}nt(t,i,s){this.it.forEach((n=>{n.nt(t,i,s)}))}}class R{nt(t,i,s){t.useBitmapCoordinateSpace((t=>this.et(t,i,s)))}}class D extends R{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.rt||null===this.rt.lt)return;const n=this.rt.lt,e=this.rt,r=Math.max(1,Math.floor(i))%2/2,h=h=>{t.beginPath();for(let a=n.to-1;a>=n.from;--a){const n=e.ot[a],l=Math.round(n._t*i)+r,o=n.ut*s,_=h*s+r;t.moveTo(l,o),t.arc(l,o,_,0,2*Math.PI)}t.fill()};e.ct>0&&(t.fillStyle=e.dt,h(e.ft+e.ct)),t.fillStyle=e.vt,h(e.ft)}}function V(){return{ot:[{_t:0,ut:0,wt:0,gt:0}],vt:"",dt:"",ft:0,ct:0,lt:null}}const I={from:0,to:1};class B{constructor(t,i,s){this.Mt=new T,this.bt=[],this.St=[],this.xt=!0,this.O=t,this.Ct=i,this.Pt=s,this.Mt.st(this.bt)}kt(t){this.yt(),this.xt=!0}Tt(){return this.xt&&(this.Rt(),this.xt=!1),this.Mt}yt(){const t=this.Pt.Dt();t.length!==this.bt.length&&(this.St=t.map(V),this.bt=this.St.map((t=>{const i=new D;return i.ht(t),i})),this.Mt.st(this.bt))}Rt(){const t=2===this.Ct.N().mode||!this.Ct.Vt(),i=this.Pt.It(),s=this.Ct.Bt(),n=this.O.Et();this.yt(),i.forEach(((i,e)=>{const r=this.St[e],h=i.At(s),a=i.zt();!t&&null!==h&&i.Vt()&&null!==a?(r.vt=h.Lt,r.ft=h.ft,r.ct=h.Ot,r.ot[0].gt=h.gt,r.ot[0].ut=i.Ft().Nt(h.gt,a.Wt),r.dt=h.Ht??this.O.Ut(r.ot[0].ut/i.Ft().$t()),r.ot[0].wt=s,r.ot[0]._t=n.qt(s),r.lt=I):r.lt=null}))}}class E extends R{constructor(t){super(),this.Yt=t}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(null===this.Yt)return;const e=this.Yt.jt.Vt,r=this.Yt.Kt.Vt;if(!e&&!r)return;const h=Math.round(this.Yt._t*s),o=Math.round(this.Yt.ut*n);t.lineCap="butt",e&&h>=0&&(t.lineWidth=Math.floor(this.Yt.jt.ct*s),t.strokeStyle=this.Yt.jt.R,t.fillStyle=this.Yt.jt.R,a(t,this.Yt.jt.Xt),function(t,i,s,n){t.beginPath();const e=t.lineWidth%2?.5:0;t.moveTo(i+e,s),t.lineTo(i+e,n),t.stroke()}(t,h,0,i.height)),r&&o>=0&&(t.lineWidth=Math.floor(this.Yt.Kt.ct*n),t.strokeStyle=this.Yt.Kt.R,t.fillStyle=this.Yt.Kt.R,a(t,this.Yt.Kt.Xt),l(t,o,0,i.width))}}class A{constructor(t,i){this.xt=!0,this.Zt={jt:{ct:1,Xt:0,R:"",Vt:!1},Kt:{ct:1,Xt:0,R:"",Vt:!1},_t:0,ut:0},this.Gt=new E(this.Zt),this.Jt=t,this.Pt=i}kt(){this.xt=!0}Tt(t){return this.xt&&(this.Rt(),this.xt=!1),this.Gt}Rt(){const t=this.Jt.Vt(),i=this.Pt.Qt().N().crosshair,s=this.Zt;if(2===i.mode)return s.Kt.Vt=!1,void(s.jt.Vt=!1);s.Kt.Vt=t&&this.Jt.ti(this.Pt),s.jt.Vt=t&&this.Jt.ii(),s.Kt.ct=i.horzLine.width,s.Kt.Xt=i.horzLine.style,s.Kt.R=i.horzLine.color,s.jt.ct=i.vertLine.width,s.jt.Xt=i.vertLine.style,s.jt.R=i.vertLine.color,s._t=this.Jt.si(),s.ut=this.Jt.ni()}}function z(t,i,s,n,e,r){t.fillRect(i+r,s,n-2*r,r),t.fillRect(i+r,s+e-r,n-2*r,r),t.fillRect(i,s,r,e),t.fillRect(i+n-r,s,r,e)}function L(t,i,s,n,e,r){t.save(),t.globalCompositeOperation="copy",t.fillStyle=r,t.fillRect(i,s,n,e),t.restore()}function O(t,i,s,n,e,r){t.beginPath(),t.roundRect?t.roundRect(i,s,n,e,r):(t.lineTo(i+n-r[1],s),0!==r[1]&&t.arcTo(i+n,s,i+n,s+r[1],r[1]),t.lineTo(i+n,s+e-r[2]),0!==r[2]&&t.arcTo(i+n,s+e,i+n-r[2],s+e,r[2]),t.lineTo(i+r[3],s+e),0!==r[3]&&t.arcTo(i,s+e,i,s+e-r[3],r[3]),t.lineTo(i,s+r[0]),0!==r[0]&&t.arcTo(i,s,i+r[0],s,r[0]))}function N(t,i,s,n,e,r,h=0,a=[0,0,0,0],l=""){if(t.save(),!h||!l||l===r)return O(t,i,s,n,e,a),t.fillStyle=r,t.fill(),void t.restore();const o=h/2;var _;O(t,i+o,s+o,n-h,e-h,(_=-o,a.map((t=>0===t?t:t+_)))),"transparent"!==r&&(t.fillStyle=r,t.fill()),"transparent"!==l&&(t.lineWidth=h,t.strokeStyle=l,t.closePath(),t.stroke()),t.restore()}function F(t,i,s,n,e,r,h){t.save(),t.globalCompositeOperation="copy";const a=t.createLinearGradient(0,0,0,e);a.addColorStop(0,r),a.addColorStop(1,h),t.fillStyle=a,t.fillRect(i,s,n,e),t.restore()}class W{constructor(t,i){this.ht(t,i)}ht(t,i){this.Yt=t,this.ei=i}$t(t,i){return this.Yt.Vt?t.P+t.A+t.V:0}nt(t,i,s,n){if(!this.Yt.Vt||0===this.Yt.ri.length)return;const e=this.Yt.R,r=this.ei.Z,h=t.useBitmapCoordinateSpace((t=>{const h=t.context;h.font=i.k;const a=this.hi(t,i,s,n),l=a.ai;return a.li?N(h,l.oi,l._i,l.ui,l.ci,r,l.di,[l.ft,0,0,l.ft],r):N(h,l.fi,l._i,l.ui,l.ci,r,l.di,[0,l.ft,l.ft,0],r),this.Yt.pi&&(h.fillStyle=e,h.fillRect(l.fi,l.mi,l.wi-l.fi,l.gi)),this.Yt.Mi&&(h.fillStyle=i.D,h.fillRect(a.li?l.bi-l.di:0,l._i,l.di,l.Si-l._i)),a}));t.useMediaCoordinateSpace((({context:t})=>{const s=h.xi;t.font=i.k,t.textAlign=h.li?"right":"left",t.textBaseline="middle",t.fillStyle=e,t.fillText(this.Yt.ri,s.Ci,(s._i+s.Si)/2+s.Pi)}))}hi(t,i,s,n){const{context:e,bitmapSize:r,mediaSize:h,horizontalPixelRatio:a,verticalPixelRatio:l}=t,o=this.Yt.pi||!this.Yt.ki?i.C:0,_=this.Yt.yi?i.S:0,u=i.A+this.ei.Ti,c=i.V+this.ei.Ri,d=i.I,f=i.B,p=this.Yt.ri,v=i.P,m=s.Di(e,p),w=Math.ceil(s.Vi(e,p)),g=v+u+c,M=i.S+d+f+w+o,b=Math.max(1,Math.floor(l));let S=Math.round(g*l);S%2!=b%2&&(S+=1);const x=_>0?Math.max(1,Math.floor(_*a)):0,C=Math.round(M*a),P=Math.round(o*a),k=this.ei.Ii??this.ei.Bi,y=Math.round(k*l)-Math.floor(.5*l),T=Math.floor(y+b/2-S/2),R=T+S,D="right"===n,V=D?h.width-_:_,I=D?r.width-x:x;let B,E,A;return D?(B=I-C,E=I-P,A=V-o-d-_):(B=I+C,E=I+P,A=V+o+d),{li:D,ai:{_i:T,mi:y,Si:R,ui:C,ci:S,ft:2*a,di:x,oi:B,fi:I,wi:E,gi:b,bi:r.width},xi:{_i:T/l,Si:R/l,Ci:A,Pi:m}}}}class H{constructor(t){this.Ei={Bi:0,Z:"#000",Ri:0,Ti:0},this.Ai={ri:"",Vt:!1,pi:!0,ki:!1,Ht:"",R:"#FFF",Mi:!1,yi:!1},this.zi={ri:"",Vt:!1,pi:!1,ki:!0,Ht:"",R:"#FFF",Mi:!0,yi:!0},this.xt=!0,this.Li=new(t||W)(this.Ai,this.Ei),this.Oi=new(t||W)(this.zi,this.Ei)}ri(){return this.Ni(),this.Ai.ri}Bi(){return this.Ni(),this.Ei.Bi}kt(){this.xt=!0}$t(t,i=!1){return Math.max(this.Li.$t(t,i),this.Oi.$t(t,i))}Fi(){return this.Ei.Ii||0}Wi(t){this.Ei.Ii=t}Hi(){return this.Ni(),this.Ai.Vt||this.zi.Vt}Ui(){return this.Ni(),this.Ai.Vt}Tt(t){return this.Ni(),this.Ai.pi=this.Ai.pi&&t.N().ticksVisible,this.zi.pi=this.zi.pi&&t.N().ticksVisible,this.Li.ht(this.Ai,this.Ei),this.Oi.ht(this.zi,this.Ei),this.Li}$i(){return this.Ni(),this.Li.ht(this.Ai,this.Ei),this.Oi.ht(this.zi,this.Ei),this.Oi}Ni(){this.xt&&(this.Ai.pi=!0,this.zi.pi=!1,this.qi(this.Ai,this.zi,this.Ei))}}class U extends H{constructor(t,i,s){super(),this.Jt=t,this.Yi=i,this.ji=s}qi(t,i,s){if(t.Vt=!1,2===this.Jt.N().mode)return;const n=this.Jt.N().horzLine;if(!n.labelVisible)return;const e=this.Yi.zt();if(!this.Jt.Vt()||this.Yi.Ki()||null===e)return;const r=this.Yi.Xi().X(n.labelBackgroundColor);s.Z=r.Z,t.R=r.G;const h=2/12*this.Yi.P();s.Ti=h,s.Ri=h;const a=this.ji(this.Yi);s.Bi=a.Bi,t.ri=this.Yi.Zi(a.gt,e),t.Vt=!0}}const $=/[1-9]/g;class q{constructor(){this.Yt=null}ht(t){this.Yt=t}nt(t,i){if(null===this.Yt||!1===this.Yt.Vt||0===this.Yt.ri.length)return;const s=t.useMediaCoordinateSpace((({context:t})=>(t.font=i.k,Math.round(i.Gi.Vi(t,u(this.Yt).ri,$)))));if(s<=0)return;const n=i.Ji,e=s+2*n,r=e/2,h=this.Yt.Qi;let a=this.Yt.Bi,l=Math.floor(a-r)+.5;l<0?(a+=Math.abs(0-l),l=Math.floor(a-r)+.5):l+e>h&&(a-=Math.abs(h-(l+e)),l=Math.floor(a-r)+.5);const o=l+e,_=Math.ceil(0+i.S+i.C+i.A+i.P+i.V);t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:s,verticalPixelRatio:n})=>{const e=u(this.Yt);t.fillStyle=e.Z;const r=Math.round(l*s),h=Math.round(0*n),a=Math.round(o*s),c=Math.round(_*n),d=Math.round(2*s);if(t.beginPath(),t.moveTo(r,h),t.lineTo(r,c-d),t.arcTo(r,c,r+d,c,d),t.lineTo(a-d,c),t.arcTo(a,c,a,c-d,d),t.lineTo(a,h),t.fill(),e.pi){const r=Math.round(e.Bi*s),a=h,l=Math.round((a+i.C)*n);t.fillStyle=e.R;const o=Math.max(1,Math.floor(s)),_=Math.floor(.5*s);t.fillRect(r-_,a,o,l-a)}})),t.useMediaCoordinateSpace((({context:t})=>{const s=u(this.Yt),e=0+i.S+i.C+i.A+i.P/2;t.font=i.k,t.textAlign="left",t.textBaseline="middle",t.fillStyle=s.R;const r=i.Gi.Di(t,"Apr0");t.translate(l+n,e+r),t.fillText(s.ri,0,0)}))}}class Y{constructor(t,i,s){this.xt=!0,this.Gt=new q,this.Zt={Vt:!1,Z:"#4c525e",R:"white",ri:"",Qi:0,Bi:NaN,pi:!0},this.Ct=t,this.ts=i,this.ji=s}kt(){this.xt=!0}Tt(){return this.xt&&(this.Rt(),this.xt=!1),this.Gt.ht(this.Zt),this.Gt}Rt(){const t=this.Zt;if(t.Vt=!1,2===this.Ct.N().mode)return;const i=this.Ct.N().vertLine;if(!i.labelVisible)return;const s=this.ts.Et();if(s.Ki())return;t.Qi=s.Qi();const n=this.ji();if(null===n)return;t.Bi=n.Bi;const e=s.ss(this.Ct.Bt());t.ri=s.ns(u(e)),t.Vt=!0;const r=this.ts.Xi().X(i.labelBackgroundColor);t.Z=r.Z,t.R=r.G,t.pi=s.N().ticksVisible}}class j{constructor(){this.es=null,this.rs=0}hs(){return this.rs}ls(t){this.rs=t}Ft(){return this.es}_s(t){this.es=t}us(t){return[]}cs(){return[]}Vt(){return!0}}var K;!function(t){t[t.Normal=0]="Normal",t[t.Magnet=1]="Magnet",t[t.Hidden=2]="Hidden",t[t.MagnetOHLC=3]="MagnetOHLC"}(K||(K={}));class X extends j{constructor(t,i){super(),this.Pt=null,this.ds=NaN,this.fs=0,this.ps=!1,this.vs=new Map,this.ws=!1,this.gs=new WeakMap,this.Ms=new WeakMap,this.bs=NaN,this.Ss=NaN,this.xs=NaN,this.Cs=NaN,this.ts=t,this.Ps=i;this.ks=((t,i)=>s=>{const n=i(),e=t();if(s===u(this.Pt).ys())return{gt:e,Bi:n};{const t=u(s.zt());return{gt:s.Ts(n,t),Bi:n}}})((()=>this.ds),(()=>this.Ss));const s=((t,i)=>()=>{const s=this.ts.Et().Rs(t()),n=i();return s&&Number.isFinite(n)?{wt:s,Bi:n}:null})((()=>this.fs),(()=>this.si()));this.Ds=new Y(this,t,s)}N(){return this.Ps}Vs(t,i){this.xs=t,this.Cs=i}Is(){this.xs=NaN,this.Cs=NaN}Bs(){return this.xs}Es(){return this.Cs}As(t,i,s){this.ws||(this.ws=!0),this.ps=!0,this.zs(t,i,s)}Bt(){return this.fs}si(){return this.bs}ni(){return this.Ss}Vt(){return this.ps}Ls(){this.ps=!1,this.Os(),this.ds=NaN,this.bs=NaN,this.Ss=NaN,this.Pt=null,this.Is(),this.Ns()}Fs(t){let i=this.gs.get(t);i||(i=new A(this,t),this.gs.set(t,i));let s=this.Ms.get(t);return s||(s=new B(this.ts,this,t),this.Ms.set(t,s)),[i,s]}ti(t){return t===this.Pt&&this.Ps.horzLine.visible}ii(){return this.Ps.vertLine.visible}Ws(t,i){this.ps&&this.Pt===t||this.vs.clear();const s=[];return this.Pt===t&&s.push(this.Hs(this.vs,i,this.ks)),s}cs(){return this.ps?[this.Ds]:[]}Us(){return this.Pt}Ns(){this.ts.$s().forEach((t=>{this.gs.get(t)?.kt(),this.Ms.get(t)?.kt()})),this.vs.forEach((t=>t.kt())),this.Ds.kt()}qs(t){return t&&!t.ys().Ki()?t.ys():null}zs(t,i,s){this.Ys(t,i,s)&&this.Ns()}Ys(t,i,s){const n=this.bs,e=this.Ss,r=this.ds,h=this.fs,a=this.Pt,l=this.qs(s);this.fs=t,this.bs=isNaN(t)?NaN:this.ts.Et().qt(t),this.Pt=s;const o=null!==l?l.zt():null;return null!==l&&null!==o?(this.ds=i,this.Ss=l.Nt(i,o)):(this.ds=NaN,this.Ss=NaN),n!==this.bs||e!==this.Ss||h!==this.fs||r!==this.ds||a!==this.Pt}Os(){const t=this.ts.js().map((t=>t.Xs().Ks())).filter(M),i=0===t.length?null:Math.max(...t);this.fs=null!==i?i:NaN}Hs(t,i,s){let n=t.get(i);return void 0===n&&(n=new U(this,i,s),t.set(i,n)),n}}function Z(t){return"left"===t||"right"===t}class G{constructor(t){this.Zs=new Map,this.Gs=[],this.Js=t}Qs(t,i){const s=function(t,i){return void 0===t?i:{tn:Math.max(t.tn,i.tn),sn:t.sn||i.sn}}(this.Zs.get(t),i);this.Zs.set(t,s)}nn(){return this.Js}en(t){const i=this.Zs.get(t);return void 0===i?{tn:this.Js}:{tn:Math.max(this.Js,i.tn),sn:i.sn}}rn(){this.hn(),this.Gs=[{an:0}]}ln(t){this.hn(),this.Gs=[{an:1,Wt:t}]}_n(t){this.un(),this.Gs.push({an:5,Wt:t})}hn(){this.un(),this.Gs.push({an:6})}cn(){this.hn(),this.Gs=[{an:4}]}dn(t){this.hn(),this.Gs.push({an:2,Wt:t})}fn(t){this.hn(),this.Gs.push({an:3,Wt:t})}pn(){return this.Gs}vn(t){for(const i of t.Gs)this.mn(i);this.Js=Math.max(this.Js,t.Js),t.Zs.forEach(((t,i)=>{this.Qs(i,t)}))}static wn(){return new G(2)}static gn(){return new G(3)}mn(t){switch(t.an){case 0:this.rn();break;case 1:this.ln(t.Wt);break;case 2:this.dn(t.Wt);break;case 3:this.fn(t.Wt);break;case 4:this.cn();break;case 5:this._n(t.Wt);break;case 6:this.un()}}un(){const t=this.Gs.findIndex((t=>5===t.an));-1!==t&&this.Gs.splice(t,1)}}class J{formatTickmarks(t){return t.map((t=>this.format(t)))}}const Q=".";function tt(t,i){if(!p(t))return"n/a";if(!v(i))throw new TypeError("invalid length");if(i<0||i>16)throw new TypeError("invalid length");if(0===i)return t.toString();return("0000000000000000"+t.toString()).slice(-i)}class it extends J{constructor(t,i){if(super(),i||(i=1),p(t)&&v(t)||(t=100),t<0)throw new TypeError("invalid base");this.Yi=t,this.Mn=i,this.bn()}format(t){const i=t<0?"−":"";return t=Math.abs(t),i+this.Sn(t)}bn(){if(this.xn=0,this.Yi>0&&this.Mn>0){let t=this.Yi;for(;t>1;)t/=10,this.xn++}}Sn(t){const i=this.Yi/this.Mn;let s=Math.floor(t),n="";const e=void 0!==this.xn?this.xn:NaN;if(i>1){let r=+(Math.round(t*i)-s*i).toFixed(this.xn);r>=i&&(r-=i,s+=1),n=Q+tt(+r.toFixed(this.xn)*this.Mn,e)}else s=Math.round(s*i)/i,e>0&&(n=Q+tt(0,e));return s.toFixed(0)+n}}class st extends it{constructor(t=100){super(t)}format(t){return`${super.format(t)}%`}}class nt extends J{constructor(t){super(),this.Cn=t}format(t){let i="";return t<0&&(i="-",t=-t),t<995?i+this.Pn(t):t<999995?i+this.Pn(t/1e3)+"K":t<999999995?(t=1e3*Math.round(t/1e3),i+this.Pn(t/1e6)+"M"):(t=1e6*Math.round(t/1e6),i+this.Pn(t/1e9)+"B")}Pn(t){let i;const s=Math.pow(10,this.Cn);return i=(t=Math.round(t*s)/s)>=1e-15&&t<1?t.toFixed(this.Cn).replace(/\.?0+$/,""):String(t),i.replace(/(\.[1-9]*)0+$/,((t,i)=>i))}}const et=/[2-9]/g;class rt{constructor(t=50){this.kn=0,this.yn=1,this.Tn=1,this.Rn={},this.Dn=new Map,this.Vn=t}In(){this.kn=0,this.Dn.clear(),this.yn=1,this.Tn=1,this.Rn={}}Vi(t,i,s){return this.Bn(t,i,s).width}Di(t,i,s){const n=this.Bn(t,i,s);return((n.actualBoundingBoxAscent||0)-(n.actualBoundingBoxDescent||0))/2}Bn(t,i,s){const n=s||et,e=String(i).replace(n,"0");if(this.Dn.has(e))return _(this.Dn.get(e)).En;if(this.kn===this.Vn){const t=this.Rn[this.Tn];delete this.Rn[this.Tn],this.Dn.delete(t),this.Tn++,this.kn--}t.save(),t.textBaseline="middle";const r=t.measureText(e);return t.restore(),0===r.width&&i.length||(this.Dn.set(e,{En:r,An:this.yn}),this.Rn[this.yn]=e,this.kn++,this.yn++),r}}class ht{constructor(t){this.zn=null,this.M=null,this.Ln="right",this.On=t}Nn(t,i,s){this.zn=t,this.M=i,this.Ln=s}nt(t){null!==this.M&&null!==this.zn&&this.zn.nt(t,this.M,this.On,this.Ln)}}class at{constructor(t,i,s){this.Fn=t,this.On=new rt(50),this.Wn=i,this.O=s,this.F=-1,this.Gt=new ht(this.On)}Tt(){const t=this.O.Hn(this.Wn);if(null===t)return null;const i=t.Un(this.Wn)?t.$n():this.Wn.Ft();if(null===i)return null;const s=t.qn(i);if("overlay"===s)return null;const n=this.O.Yn();return n.P!==this.F&&(this.F=n.P,this.On.In()),this.Gt.Nn(this.Fn.$i(),n,s),this.Gt}}class lt extends R{constructor(){super(...arguments),this.Yt=null}ht(t){this.Yt=t}jn(t,i){if(!this.Yt?.Vt)return null;const{ut:s,ct:n,Kn:e}=this.Yt;return i>=s-n-7&&i<=s+n+7?{Xn:this.Yt,Kn:e}:null}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(null===this.Yt)return;if(!1===this.Yt.Vt)return;const e=Math.round(this.Yt.ut*n);e<0||e>i.height||(t.lineCap="butt",t.strokeStyle=this.Yt.R,t.lineWidth=Math.floor(this.Yt.ct*s),a(t,this.Yt.Xt),l(t,e,0,i.width))}}class ot{constructor(t){this.Zn={ut:0,R:"rgba(0, 0, 0, 0)",ct:1,Xt:0,Vt:!1},this.Gn=new lt,this.xt=!0,this.Jn=t,this.Qn=t.Qt(),this.Gn.ht(this.Zn)}kt(){this.xt=!0}Tt(){return this.Jn.Vt()?(this.xt&&(this.te(),this.xt=!1),this.Gn):null}}class _t extends ot{constructor(t){super(t)}te(){this.Zn.Vt=!1;const t=this.Jn.Ft(),i=t.ie().ie;if(2!==i&&3!==i)return;const s=this.Jn.N();if(!s.baseLineVisible||!this.Jn.Vt())return;const n=this.Jn.zt();null!==n&&(this.Zn.Vt=!0,this.Zn.ut=t.Nt(n.Wt,n.Wt),this.Zn.R=s.baseLineColor,this.Zn.ct=s.baseLineWidth,this.Zn.Xt=s.baseLineStyle)}}class ut extends R{constructor(){super(...arguments),this.Yt=null}ht(t){this.Yt=t}se(){return this.Yt}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){const n=this.Yt;if(null===n)return;const e=Math.max(1,Math.floor(i)),r=e%2/2,h=Math.round(n.ne.x*i)+r,a=n.ne.y*s;t.fillStyle=n.ee,t.beginPath();const l=Math.max(2,1.5*n.re)*i;t.arc(h,a,l,0,2*Math.PI,!1),t.fill(),t.fillStyle=n.he,t.beginPath(),t.arc(h,a,n.ft*i,0,2*Math.PI,!1),t.fill(),t.lineWidth=e,t.strokeStyle=n.ae,t.beginPath(),t.arc(h,a,n.ft*i+e/2,0,2*Math.PI,!1),t.stroke()}}const ct=[{le:0,oe:.25,_e:4,ue:10,ce:.25,de:0,fe:.4,pe:.8},{le:.25,oe:.525,_e:10,ue:14,ce:0,de:0,fe:.8,pe:0},{le:.525,oe:1,_e:14,ue:14,ce:0,de:0,fe:0,pe:0}];class dt{constructor(t){this.Gt=new ut,this.xt=!0,this.ve=!0,this.me=performance.now(),this.we=this.me-1,this.ge=t}Me(){this.we=this.me-1,this.kt()}be(){if(this.kt(),2===this.ge.N().lastPriceAnimation){const t=performance.now(),i=this.we-t;if(i>0)return void(i<650&&(this.we+=2600));this.me=t,this.we=t+2600}}kt(){this.xt=!0}Se(){this.ve=!0}Vt(){return 0!==this.ge.N().lastPriceAnimation}xe(){switch(this.ge.N().lastPriceAnimation){case 0:return!1;case 1:return!0;case 2:return performance.now()<=this.we}}Tt(){return this.xt?(this.Rt(),this.xt=!1,this.ve=!1):this.ve&&(this.Ce(),this.ve=!1),this.Gt}Rt(){this.Gt.ht(null);const t=this.ge.Qt().Et(),i=t.Pe(),s=this.ge.zt();if(null===i||null===s)return;const n=this.ge.ke(!0);if(n.ye||!i.Te(n.Re))return;const e={x:t.qt(n.Re),y:this.ge.Ft().Nt(n.gt,s.Wt)},r=n.R,h=this.ge.N().lineWidth,a=this.De(this.Ve(),r);this.Gt.ht({ee:r,re:h,he:a.he,ae:a.ae,ft:a.ft,ne:e})}Ce(){const t=this.Gt.se();if(null!==t){const i=this.De(this.Ve(),t.ee);t.he=i.he,t.ae=i.ae,t.ft=i.ft}}Ve(){return this.xe()?performance.now()-this.me:2599}Ie(t,i,s,n){const e=s+(n-s)*i;return this.ge.Qt().Xi().j(t,e)}De(t,i){const s=t%2600/2600;let n;for(const t of ct)if(s>=t.le&&s<=t.oe){n=t;break}o(void 0!==n,"Last price animation internal logic error");const e=(s-n.le)/(n.oe-n.le);return{he:this.Ie(i,e,n.ce,n.de),ae:this.Ie(i,e,n.fe,n.pe),ft:(r=e,h=n._e,a=n.ue,h+(a-h)*r)};var r,h,a}}class ft extends ot{constructor(t){super(t)}te(){const t=this.Zn;t.Vt=!1;const i=this.Jn.N();if(!i.priceLineVisible||!this.Jn.Vt())return;const s=this.Jn.ke(0===i.priceLineSource);s.ye||(t.Vt=!0,t.ut=s.Bi,t.R=this.Jn.Be(s.R),t.ct=i.priceLineWidth,t.Xt=i.priceLineStyle)}}class pt extends H{constructor(t){super(),this.Jt=t}qi(t,i,s){t.Vt=!1,i.Vt=!1;const n=this.Jt;if(!n.Vt())return;const e=n.N(),r=e.lastValueVisible,h=""!==n.Ee(),a=0===e.seriesLastValueMode,l=n.ke(!1);if(l.ye)return;r&&(t.ri=this.Ae(l,r,a),t.Vt=0!==t.ri.length),(h||a)&&(i.ri=this.ze(l,r,h,a),i.Vt=i.ri.length>0);const o=n.Be(l.R),_=this.Jt.Qt().Xi().X(o);s.Z=_.Z,s.Bi=l.Bi,i.Ht=n.Qt().Ut(l.Bi/n.Ft().$t()),t.Ht=o,t.R=_.G,i.R=_.G}ze(t,i,s,n){let e="";const r=this.Jt.Ee();return s&&0!==r.length&&(e+=`${r} `),i&&n&&(e+=this.Jt.Ft().Le()?t.Oe:t.Ne),e.trim()}Ae(t,i,s){return i?s?this.Jt.Ft().Le()?t.Ne:t.Oe:t.ri:""}}function vt(t,i,s,n){const e=Number.isFinite(i),r=Number.isFinite(s);return e&&r?t(i,s):e||r?e?i:s:n}class mt{constructor(t,i){this.Fe=t,this.We=i}He(t){return null!==t&&(this.Fe===t.Fe&&this.We===t.We)}Ue(){return new mt(this.Fe,this.We)}$e(){return this.Fe}qe(){return this.We}Ye(){return this.We-this.Fe}Ki(){return this.We===this.Fe||Number.isNaN(this.We)||Number.isNaN(this.Fe)}vn(t){return null===t?this:new mt(vt(Math.min,this.$e(),t.$e(),-1/0),vt(Math.max,this.qe(),t.qe(),1/0))}je(t){if(!p(t))return;if(0===this.We-this.Fe)return;const i=.5*(this.We+this.Fe);let s=this.We-i,n=this.Fe-i;s*=t,n*=t,this.We=i+s,this.Fe=i+n}Ke(t){p(t)&&(this.We+=t,this.Fe+=t)}Xe(){return{minValue:this.Fe,maxValue:this.We}}static Ze(t){return null===t?null:new mt(t.minValue,t.maxValue)}}class wt{constructor(t,i){this.Ge=t,this.Je=i||null}Qe(){return this.Ge}tr(){return this.Je}Xe(){return{priceRange:null===this.Ge?null:this.Ge.Xe(),margins:this.Je||void 0}}static Ze(t){return null===t?null:new wt(mt.Ze(t.priceRange),t.margins)}}class gt extends ot{constructor(t,i){super(t),this.ir=i}te(){const t=this.Zn;t.Vt=!1;const i=this.ir.N();if(!this.Jn.Vt()||!i.lineVisible)return;const s=this.ir.sr();null!==s&&(t.Vt=!0,t.ut=s,t.R=i.color,t.ct=i.lineWidth,t.Xt=i.lineStyle,t.Kn=this.ir.N().id)}}class Mt extends H{constructor(t,i){super(),this.ge=t,this.ir=i}qi(t,i,s){t.Vt=!1,i.Vt=!1;const n=this.ir.N(),e=n.axisLabelVisible,r=""!==n.title,h=this.ge;if(!e||!h.Vt())return;const a=this.ir.sr();if(null===a)return;r&&(i.ri=n.title,i.Vt=!0),i.Ht=h.Qt().Ut(a/h.Ft().$t()),t.ri=this.nr(n.price),t.Vt=!0;const l=this.ge.Qt().Xi().X(n.axisLabelColor||n.color);s.Z=l.Z;const o=n.axisLabelTextColor||l.G;t.R=o,i.R=o,s.Bi=a}nr(t){const i=this.ge.zt();return null===i?"":this.ge.Ft().Zi(t,i.Wt)}}class bt{constructor(t,i){this.ge=t,this.Ps=i,this.er=new gt(t,this),this.Fn=new Mt(t,this),this.rr=new at(this.Fn,t,t.Qt())}hr(t){f(this.Ps,t),this.kt(),this.ge.Qt().ar()}N(){return this.Ps}lr(){return this.er}_r(){return this.rr}ur(){return this.Fn}kt(){this.er.kt(),this.Fn.kt()}sr(){const t=this.ge,i=t.Ft();if(t.Qt().Et().Ki()||i.Ki())return null;const s=t.zt();return null===s?null:i.Nt(this.Ps.price,s.Wt)}}class St extends j{constructor(t){super(),this.ts=t}Qt(){return this.ts}}const xt={Bar:(t,i,s,n)=>{const e=i.upColor,r=i.downColor,h=u(t(s,n)),a=c(h.Wt[0])<=c(h.Wt[3]);return{cr:h.R??(a?e:r)}},Candlestick:(t,i,s,n)=>{const e=i.upColor,r=i.downColor,h=i.borderUpColor,a=i.borderDownColor,l=i.wickUpColor,o=i.wickDownColor,_=u(t(s,n)),d=c(_.Wt[0])<=c(_.Wt[3]);return{cr:_.R??(d?e:r),dr:_.Ht??(d?h:a),pr:_.vr??(d?l:o)}},Custom:(t,i,s,n)=>({cr:u(t(s,n)).R??i.color}),Area:(t,i,s,n)=>{const e=u(t(s,n));return{cr:e.vt??i.lineColor,vt:e.vt??i.lineColor,mr:e.mr??i.topColor,wr:e.wr??i.bottomColor}},Baseline:(t,i,s,n)=>{const e=u(t(s,n));return{cr:e.Wt[3]>=i.baseValue.price?i.topLineColor:i.bottomLineColor,gr:e.gr??i.topLineColor,Mr:e.Mr??i.bottomLineColor,br:e.br??i.topFillColor1,Sr:e.Sr??i.topFillColor2,Cr:e.Cr??i.bottomFillColor1,Pr:e.Pr??i.bottomFillColor2}},Line:(t,i,s,n)=>{const e=u(t(s,n));return{cr:e.R??i.color,vt:e.R??i.color}},Histogram:(t,i,s,n)=>({cr:u(t(s,n)).R??i.color})};class Ct{constructor(t){this.kr=(t,i)=>void 0!==i?i.Wt:this.ge.Xs().yr(t),this.ge=t,this.Tr=xt[t.Rr()]}Dr(t,i){return this.Tr(this.kr,this.ge.N(),t,i)}}function Pt(t,i,s,n,e=0,r=i.length){let h=r-e;for(;0<h;){const r=h>>1,a=e+r;n(i[a],s)===t?(e=a+1,h-=r+1):h=r}return e}const kt=Pt.bind(null,!0),yt=Pt.bind(null,!1);var Tt;!function(t){t[t.NearestLeft=-1]="NearestLeft",t[t.None=0]="None",t[t.NearestRight=1]="NearestRight"}(Tt||(Tt={}));const Rt=30;class Dt{constructor(){this.Vr=[],this.Ir=new Map,this.Br=new Map,this.Er=[]}Ar(){return this.zr()>0?this.Vr[this.Vr.length-1]:null}Lr(){return this.zr()>0?this.Or(0):null}Ks(){return this.zr()>0?this.Or(this.Vr.length-1):null}zr(){return this.Vr.length}Ki(){return 0===this.zr()}Te(t){return null!==this.Nr(t,0)}yr(t){return this.Fr(t)}Fr(t,i=0){const s=this.Nr(t,i);return null===s?null:{...this.Wr(s),Re:this.Or(s)}}Hr(){return this.Vr}Ur(t,i,s){if(this.Ki())return null;let n=null;for(const e of s){n=Vt(n,this.$r(t,i,e))}return n}ht(t){this.Br.clear(),this.Ir.clear(),this.Vr=t,this.Er=t.map((t=>t.Re))}qr(){return this.Er}Or(t){return this.Vr[t].Re}Wr(t){return this.Vr[t]}Nr(t,i){const s=this.Yr(t);if(null===s&&0!==i)switch(i){case-1:return this.jr(t);case 1:return this.Kr(t);default:throw new TypeError("Unknown search mode")}return s}jr(t){let i=this.Xr(t);return i>0&&(i-=1),i!==this.Vr.length&&this.Or(i)<t?i:null}Kr(t){const i=this.Zr(t);return i!==this.Vr.length&&t<this.Or(i)?i:null}Yr(t){const i=this.Xr(t);return i===this.Vr.length||t<this.Vr[i].Re?null:i}Xr(t){return kt(this.Vr,t,((t,i)=>t.Re<i))}Zr(t){return yt(this.Vr,t,((t,i)=>t.Re>i))}Gr(t,i,s){let n=null;for(let e=t;e<i;e++){const t=this.Vr[e].Wt[s];Number.isNaN(t)||(null===n?n={Jr:t,Qr:t}:(t<n.Jr&&(n.Jr=t),t>n.Qr&&(n.Qr=t)))}return n}$r(t,i,s){if(this.Ki())return null;let n=null;const e=u(this.Lr()),r=u(this.Ks()),h=Math.max(t,e),a=Math.min(i,r),l=Math.ceil(h/Rt)*Rt,o=Math.max(l,Math.floor(a/Rt)*Rt);{const t=this.Xr(h),e=this.Zr(Math.min(a,l,i));n=Vt(n,this.Gr(t,e,s))}let _=this.Ir.get(s);void 0===_&&(_=new Map,this.Ir.set(s,_));for(let t=Math.max(l+1,h);t<o;t+=Rt){const i=Math.floor(t/Rt);let e=_.get(i);if(void 0===e){const t=this.Xr(i*Rt),n=this.Zr((i+1)*Rt-1);e=this.Gr(t,n,s),_.set(i,e)}n=Vt(n,e)}{const t=this.Xr(o),i=this.Zr(a);n=Vt(n,this.Gr(t,i,s))}return n}}function Vt(t,i){if(null===t)return i;if(null===i)return t;return{Jr:Math.min(t.Jr,i.Jr),Qr:Math.max(t.Qr,i.Qr)}}class It{constructor(t){this.th=t}nt(t,i,s){this.th.draw(t)}ih(t,i,s){this.th.drawBackground?.(t)}}class Bt{constructor(t){this.Dn=null,this.sh=t}Tt(){const t=this.sh.renderer();if(null===t)return null;if(this.Dn?.nh===t)return this.Dn.eh;const i=new It(t);return this.Dn={nh:t,eh:i},i}rh(){return this.sh.zOrder?.()??"normal"}}class Et{constructor(t){this.hh=null,this.ah=t}oh(){return this.ah}Ns(){this.ah.updateAllViews?.()}Fs(){const t=this.ah.paneViews?.()??[];if(this.hh?.nh===t)return this.hh.eh;const i=t.map((t=>new Bt(t)));return this.hh={nh:t,eh:i},i}jn(t,i){return this.ah.hitTest?.(t,i)??null}}let At=class extends Et{us(){return[]}};class zt{constructor(t){this.th=t}nt(t,i,s){this.th.draw(t)}ih(t,i,s){this.th.drawBackground?.(t)}}class Lt{constructor(t){this.Dn=null,this.sh=t}Tt(){const t=this.sh.renderer();if(null===t)return null;if(this.Dn?.nh===t)return this.Dn.eh;const i=new zt(t);return this.Dn={nh:t,eh:i},i}rh(){return this.sh.zOrder?.()??"normal"}}function Ot(t){return{ri:t.text(),Bi:t.coordinate(),Ii:t.fixedCoordinate?.(),R:t.textColor(),Z:t.backColor(),Vt:t.visible?.()??!0,pi:t.tickVisible?.()??!0}}class Nt{constructor(t,i){this.Gt=new q,this._h=t,this.uh=i}Tt(){return this.Gt.ht({Qi:this.uh.Qi(),...Ot(this._h)}),this.Gt}}class Ft extends H{constructor(t,i){super(),this._h=t,this.Yi=i}qi(t,i,s){const n=Ot(this._h);s.Z=n.Z,t.R=n.R;const e=2/12*this.Yi.P();s.Ti=e,s.Ri=e,s.Bi=n.Bi,s.Ii=n.Ii,t.ri=n.ri,t.Vt=n.Vt,t.pi=n.pi}}class Wt extends Et{constructor(t,i){super(t),this.dh=null,this.fh=null,this.ph=null,this.mh=null,this.ge=i}cs(){const t=this.ah.timeAxisViews?.()??[];if(this.dh?.nh===t)return this.dh.eh;const i=this.ge.Qt().Et(),s=t.map((t=>new Nt(t,i)));return this.dh={nh:t,eh:s},s}Ws(){const t=this.ah.priceAxisViews?.()??[];if(this.fh?.nh===t)return this.fh.eh;const i=this.ge.Ft(),s=t.map((t=>new Ft(t,i)));return this.fh={nh:t,eh:s},s}wh(){const t=this.ah.priceAxisPaneViews?.()??[];if(this.ph?.nh===t)return this.ph.eh;const i=t.map((t=>new Lt(t)));return this.ph={nh:t,eh:i},i}gh(){const t=this.ah.timeAxisPaneViews?.()??[];if(this.mh?.nh===t)return this.mh.eh;const i=t.map((t=>new Lt(t)));return this.mh={nh:t,eh:i},i}Mh(t,i){return this.ah.autoscaleInfo?.(t,i)??null}}function Ht(t,i,s,n){t.forEach((t=>{i(t).forEach((t=>{t.rh()===s&&n.push(t)}))}))}function Ut(t){return t.Fs()}function $t(t){return t.wh()}function qt(t){return t.gh()}const Yt=["Area","Line","Baseline"];class jt extends St{constructor(t,i,s,n,e){super(t),this.Yt=new Dt,this.er=new ft(this),this.bh=[],this.Sh=new _t(this),this.xh=null,this.Ch=null,this.Ph=null,this.kh=[],this.Ps=s,this.yh=i;const r=new pt(this);this.vs=[r],this.rr=new at(r,this,t),Yt.includes(this.yh)&&(this.xh=new dt(this)),this.Th(),this.sh=n(this,this.Qt(),e)}m(){null!==this.Ph&&clearTimeout(this.Ph)}Be(t){return this.Ps.priceLineColor||t}ke(t){const i={ye:!0},s=this.Ft();if(this.Qt().Et().Ki()||s.Ki()||this.Yt.Ki())return i;const n=this.Qt().Et().Pe(),e=this.zt();if(null===n||null===e)return i;let r,h;if(t){const t=this.Yt.Ar();if(null===t)return i;r=t,h=t.Re}else{const t=this.Yt.Fr(n.bi(),-1);if(null===t)return i;if(r=this.Yt.yr(t.Re),null===r)return i;h=t.Re}const a=r.Wt[3],l=this.Rh().Dr(h,{Wt:r}),o=s.Nt(a,e.Wt);return{ye:!1,gt:a,ri:s.Zi(a,e.Wt),Oe:s.Dh(a),Ne:s.Vh(a,e.Wt),R:l.cr,Bi:o,Re:h}}Rh(){return null!==this.Ch||(this.Ch=new Ct(this)),this.Ch}N(){return this.Ps}hr(t){const i=t.priceScaleId;void 0!==i&&i!==this.Ps.priceScaleId&&this.Qt().Ih(this,i),f(this.Ps,t),void 0!==t.priceFormat&&(this.Th(),this.Qt().Bh()),this.Qt().Eh(this),this.Qt().Ah(),this.sh.kt("options")}ht(t,i){this.Yt.ht(t),this.sh.kt("data"),null!==this.xh&&(i&&i.zh?this.xh.be():0===t.length&&this.xh.Me());const s=this.Qt().Hn(this);this.Qt().Lh(s),this.Qt().Eh(this),this.Qt().Ah(),this.Qt().ar()}Oh(t){const i=new bt(this,t);return this.bh.push(i),this.Qt().Eh(this),i}Nh(t){const i=this.bh.indexOf(t);-1!==i&&this.bh.splice(i,1),this.Qt().Eh(this)}Fh(){return this.bh}Rr(){return this.yh}zt(){const t=this.Wh();return null===t?null:{Wt:t.Wt[3],Hh:t.wt}}Wh(){const t=this.Qt().Et().Pe();if(null===t)return null;const i=t.Uh();return this.Yt.Fr(i,1)}Xs(){return this.Yt}$h(t){const i=this.Yt.yr(t);return null===i?null:"Bar"===this.yh||"Candlestick"===this.yh||"Custom"===this.yh?{qh:i.Wt[0],Yh:i.Wt[1],jh:i.Wt[2],Kh:i.Wt[3]}:i.Wt[3]}Xh(t){const i=[];Ht(this.kh,Ut,"top",i);const s=this.xh;return null!==s&&s.Vt()?(null===this.Ph&&s.xe()&&(this.Ph=setTimeout((()=>{this.Ph=null,this.Qt().Zh()}),0)),s.Se(),i.unshift(s),i):i}Fs(){const t=[];this.Gh()||t.push(this.Sh),t.push(this.sh,this.er);const i=this.bh.map((t=>t.lr()));return t.push(...i),Ht(this.kh,Ut,"normal",t),t}Jh(){return this.Qh(Ut,"bottom")}ta(t){return this.Qh($t,t)}ia(t){return this.Qh(qt,t)}sa(t,i){return this.kh.map((s=>s.jn(t,i))).filter((t=>null!==t))}us(){return[this.rr,...this.bh.map((t=>t._r()))]}Ws(t,i){if(i!==this.es&&!this.Gh())return[];const s=[...this.vs];for(const t of this.bh)s.push(t.ur());return this.kh.forEach((t=>{s.push(...t.Ws())})),s}cs(){const t=[];return this.kh.forEach((i=>{t.push(...i.cs())})),t}Mh(t,i){if(void 0!==this.Ps.autoscaleInfoProvider){const s=this.Ps.autoscaleInfoProvider((()=>{const s=this.na(t,i);return null===s?null:s.Xe()}));return wt.Ze(s)}return this.na(t,i)}ea(){return this.Ps.priceFormat.minMove}ra(){return this.ha}Ns(){this.sh.kt();for(const t of this.vs)t.kt();for(const t of this.bh)t.kt();this.er.kt(),this.Sh.kt(),this.xh?.kt(),this.kh.forEach((t=>t.Ns()))}Ft(){return u(super.Ft())}At(t){if(!(("Line"===this.yh||"Area"===this.yh||"Baseline"===this.yh)&&this.Ps.crosshairMarkerVisible))return null;const i=this.Yt.yr(t);if(null===i)return null;return{gt:i.Wt[3],ft:this.aa(),Ht:this.la(),Ot:this.oa(),Lt:this._a(t)}}Ee(){return this.Ps.title}Vt(){return this.Ps.visible}ua(t){this.kh.push(new Wt(t,this))}ca(t){this.kh=this.kh.filter((i=>i.oh()!==t))}da(){if("Custom"===this.yh)return t=>this.sh.fa(t)}pa(){if("Custom"===this.yh)return t=>this.sh.va(t)}ma(){return this.Yt.qr()}Gh(){return!Z(this.Ft().wa())}na(t,i){if(!v(t)||!v(i)||this.Yt.Ki())return null;const s="Line"===this.yh||"Area"===this.yh||"Baseline"===this.yh||"Histogram"===this.yh?[3]:[2,1],n=this.Yt.Ur(t,i,s);let e=null!==n?new mt(n.Jr,n.Qr):null,r=null;if("Histogram"===this.Rr()){const t=this.Ps.base,i=new mt(t,t);e=null!==e?e.vn(i):i}return this.kh.forEach((s=>{const n=s.Mh(t,i);if(n?.priceRange){const t=new mt(n.priceRange.minValue,n.priceRange.maxValue);e=null!==e?e.vn(t):t}n?.margins&&(r=n.margins)})),new wt(e,r)}aa(){switch(this.yh){case"Line":case"Area":case"Baseline":return this.Ps.crosshairMarkerRadius}return 0}la(){switch(this.yh){case"Line":case"Area":case"Baseline":{const t=this.Ps.crosshairMarkerBorderColor;if(0!==t.length)return t}}return null}oa(){switch(this.yh){case"Line":case"Area":case"Baseline":return this.Ps.crosshairMarkerBorderWidth}return 0}_a(t){switch(this.yh){case"Line":case"Area":case"Baseline":{const t=this.Ps.crosshairMarkerBackgroundColor;if(0!==t.length)return t}}return this.Rh().Dr(t).cr}Th(){switch(this.Ps.priceFormat.type){case"custom":{const t=this.Ps.priceFormat.formatter;this.ha={format:t,formatTickmarks:this.Ps.priceFormat.tickmarksFormatter??(i=>i.map(t))};break}case"volume":this.ha=new nt(this.Ps.priceFormat.precision);break;case"percent":this.ha=new st(this.Ps.priceFormat.precision);break;default:{const t=Math.pow(10,this.Ps.priceFormat.precision);this.ha=new it(t,this.Ps.priceFormat.minMove*t)}}null!==this.es&&this.es.ga()}Qh(t,i){const s=[];return Ht(this.kh,t,i,s),s}}const Kt=[3],Xt=[0,1,2,3];class Zt{constructor(t){this.Ps=t}Ma(t,i,s){let n=t;if(0===this.Ps.mode)return n;const e=s.ys(),r=e.zt();if(null===r)return n;const h=e.Nt(t,r),a=s.ba().filter((t=>t instanceof jt)).reduce(((t,n)=>{if(s.Un(n)||!n.Vt())return t;const e=n.Ft(),r=n.Xs();if(e.Ki()||!r.Te(i))return t;const h=r.yr(i);if(null===h)return t;const a=c(n.zt()),l=3===this.Ps.mode?Xt:Kt;return t.concat(l.map((t=>e.Nt(h.Wt[t],a.Wt))))}),[]);if(0===a.length)return n;a.sort(((t,i)=>Math.abs(t-h)-Math.abs(i-h)));const l=a[0];return n=e.Ts(l,r),n}}function Gt(t,i,s){return Math.min(Math.max(t,i),s)}function Jt(t,i,s){return i-t<=s}function Qt(t){const i=Math.ceil(t);return i%2==0?i-1:i}class ti extends R{constructor(){super(...arguments),this.Yt=null}ht(t){this.Yt=t}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(null===this.Yt)return;const e=Math.max(1,Math.floor(s));t.lineWidth=e,function(t,i){t.save(),t.lineWidth%2&&t.translate(.5,.5),i(),t.restore()}(t,(()=>{const r=u(this.Yt);if(r.Sa){t.strokeStyle=r.xa,a(t,r.Ca),t.beginPath();for(const n of r.Pa){const r=Math.round(n.ka*s);t.moveTo(r,-e),t.lineTo(r,i.height+e)}t.stroke()}if(r.ya){t.strokeStyle=r.Ta,a(t,r.Ra),t.beginPath();for(const s of r.Da){const r=Math.round(s.ka*n);t.moveTo(-e,r),t.lineTo(i.width+e,r)}t.stroke()}}))}}class ii{constructor(t){this.Gt=new ti,this.xt=!0,this.Pt=t}kt(){this.xt=!0}Tt(){if(this.xt){const t=this.Pt.Qt().N().grid,i={ya:t.horzLines.visible,Sa:t.vertLines.visible,Ta:t.horzLines.color,xa:t.vertLines.color,Ra:t.horzLines.style,Ca:t.vertLines.style,Da:this.Pt.ys().Va(),Pa:(this.Pt.Qt().Et().Va()||[]).map((t=>({ka:t.coord})))};this.Gt.ht(i),this.xt=!1}return this.Gt}}class si{constructor(t){this.sh=new ii(t)}lr(){return this.sh}}const ni={Ia:4,Ba:1e-4};function ei(t,i){const s=100*(t-i)/i;return i<0?-s:s}function ri(t,i){const s=ei(t.$e(),i),n=ei(t.qe(),i);return new mt(s,n)}function hi(t,i){const s=100*(t-i)/i+100;return i<0?-s:s}function ai(t,i){const s=hi(t.$e(),i),n=hi(t.qe(),i);return new mt(s,n)}function li(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const n=Math.log10(s+i.Ba)+i.Ia;return t<0?-n:n}function oi(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const n=Math.pow(10,s-i.Ia)-i.Ba;return t<0?-n:n}function _i(t,i){if(null===t)return null;const s=li(t.$e(),i),n=li(t.qe(),i);return new mt(s,n)}function ui(t,i){if(null===t)return null;const s=oi(t.$e(),i),n=oi(t.qe(),i);return new mt(s,n)}function ci(t){if(null===t)return ni;const i=Math.abs(t.qe()-t.$e());if(i>=1||i<1e-15)return ni;const s=Math.ceil(Math.abs(Math.log10(i))),n=ni.Ia+s;return{Ia:n,Ba:1/Math.pow(10,n)}}class di{constructor(t,i){if(this.Ea=t,this.Aa=i,function(t){if(t<0)return!1;for(let i=t;i>1;i/=10)if(i%10!=0)return!1;return!0}(this.Ea))this.za=[2,2.5,2];else{this.za=[];for(let t=this.Ea;1!==t;){if(t%2==0)this.za.push(2),t/=2;else{if(t%5!=0)throw new Error("unexpected base");this.za.push(2,2.5),t/=5}if(this.za.length>100)throw new Error("something wrong with base")}}}La(t,i,s){const n=0===this.Ea?0:1/this.Ea;let e=Math.pow(10,Math.max(0,Math.ceil(Math.log10(t-i)))),r=0,h=this.Aa[0];for(;;){const t=Jt(e,n,1e-14)&&e>n+1e-14,i=Jt(e,s*h,1e-14),a=Jt(e,1,1e-14);if(!(t&&i&&a))break;e/=h,h=this.Aa[++r%this.Aa.length]}if(e<=n+1e-14&&(e=n),e=Math.max(1,e),this.za.length>0&&(a=e,l=1,o=1e-14,Math.abs(a-l)<o))for(r=0,h=this.za[0];Jt(e,s*h,1e-14)&&e>n+1e-14;)e/=h,h=this.za[++r%this.za.length];var a,l,o;return e}}class fi{constructor(t,i,s,n){this.Oa=[],this.Yi=t,this.Ea=i,this.Na=s,this.Fa=n}La(t,i){if(t<i)throw new Error("high < low");const s=this.Yi.$t(),n=(t-i)*this.Wa()/s,e=new di(this.Ea,[2,2.5,2]),r=new di(this.Ea,[2,2,2.5]),h=new di(this.Ea,[2.5,2,2]),a=[];return a.push(e.La(t,i,n),r.La(t,i,n),h.La(t,i,n)),function(t){if(t.length<1)throw Error("array is empty");let i=t[0];for(let s=1;s<t.length;++s)t[s]<i&&(i=t[s]);return i}(a)}Ha(){const t=this.Yi,i=t.zt();if(null===i)return void(this.Oa=[]);const s=t.$t(),n=this.Na(s-1,i),e=this.Na(0,i),r=this.Yi.N().entireTextOnly?this.Ua()/2:0,h=r,a=s-1-r,l=Math.max(n,e),o=Math.min(n,e);if(l===o)return void(this.Oa=[]);const _=this.La(l,o);if(this.$a(i,_,l,o,h,a),t.qa()&&this.Ya(_,o,l)){const t=this.Yi.ja();this.Ka(i,_,h,a,t,2*t)}const u=this.Oa.map((t=>t.Xa)),c=this.Yi.Za(u);for(let t=0;t<this.Oa.length;t++)this.Oa[t].Ga=c[t]}Va(){return this.Oa}Ua(){return this.Yi.P()}Wa(){return Math.ceil(2.5*this.Ua())}$a(t,i,s,n,e,r){const h=this.Oa,a=this.Yi;let l=s%i;l+=l<0?i:0;const o=s>=n?1:-1;let _=null,u=0;for(let c=s-l;c>n;c-=i){const s=this.Fa(c,t,!0);null!==_&&Math.abs(s-_)<this.Wa()||(s<e||s>r||(u<h.length?(h[u].ka=s,h[u].Ga=a.Ja(c),h[u].Xa=c):h.push({ka:s,Ga:a.Ja(c),Xa:c}),u++,_=s,a.Qa()&&(i=this.La(c*o,n))))}h.length=u}Ka(t,i,s,n,e,r){const h=this.Oa,a=this.tl(t,s,e,r),l=this.tl(t,n,-r,-e),o=this.Fa(0,t,!0)-this.Fa(i,t,!0);h.length>0&&h[0].ka-a.ka<o/2&&h.shift(),h.length>0&&l.ka-h[h.length-1].ka<o/2&&h.pop(),h.unshift(a),h.push(l)}tl(t,i,s,n){const e=(s+n)/2,r=this.Na(i+s,t),h=this.Na(i+n,t),a=Math.min(r,h),l=Math.max(r,h),o=Math.max(.1,this.La(l,a)),_=this.Na(i+e,t),u=_-_%o,c=this.Fa(u,t,!0);return{Ga:this.Yi.Ja(u),ka:c,Xa:u}}Ya(t,i,s){let n=c(this.Yi.Qe());return this.Yi.Qa()&&(n=ui(n,this.Yi.il())),n.$e()-i<t&&s-n.qe()<t}}function pi(t){return t.slice().sort(((t,i)=>u(t.hs())-u(i.hs())))}var vi;!function(t){t[t.Normal=0]="Normal",t[t.Logarithmic=1]="Logarithmic",t[t.Percentage=2]="Percentage",t[t.IndexedTo100=3]="IndexedTo100"}(vi||(vi={}));const mi=new st,wi=new it(100,1);class gi{constructor(t,i,s,n,e){this.sl=0,this.nl=null,this.Ge=null,this.el=null,this.rl={hl:!1,al:null},this.ll=!1,this.ol=0,this._l=0,this.ul=new d,this.cl=new d,this.dl=[],this.fl=null,this.pl=null,this.vl=null,this.ml=null,this.wl=null,this.ha=wi,this.gl=ci(null),this.Ml=t,this.Ps=i,this.bl=s,this.Sl=n,this.xl=e,this.Cl=new fi(this,100,this.Pl.bind(this),this.kl.bind(this))}wa(){return this.Ml}N(){return this.Ps}hr(t){if(f(this.Ps,t),this.ga(),void 0!==t.mode&&this.yl({ie:t.mode}),void 0!==t.scaleMargins){const i=_(t.scaleMargins.top),s=_(t.scaleMargins.bottom);if(i<0||i>1)throw new Error(`Invalid top margin - expect value between 0 and 1, given=${i}`);if(s<0||s>1)throw new Error(`Invalid bottom margin - expect value between 0 and 1, given=${s}`);if(i+s>1)throw new Error(`Invalid margins - sum of margins must be less than 1, given=${i+s}`);this.Tl(),this.vl=null}}Rl(){return this.Ps.autoScale}Dl(){return this.ll}Qa(){return 1===this.Ps.mode}Le(){return 2===this.Ps.mode}Vl(){return 3===this.Ps.mode}il(){return this.gl}ie(){return{sn:this.Ps.autoScale,Il:this.Ps.invertScale,ie:this.Ps.mode}}yl(t){const i=this.ie();let s=null;void 0!==t.sn&&(this.Ps.autoScale=t.sn),void 0!==t.ie&&(this.Ps.mode=t.ie,2!==t.ie&&3!==t.ie||(this.Ps.autoScale=!0),this.rl.hl=!1),1===i.ie&&t.ie!==i.ie&&(!function(t,i){if(null===t)return!1;const s=oi(t.$e(),i),n=oi(t.qe(),i);return isFinite(s)&&isFinite(n)}(this.Ge,this.gl)?this.Ps.autoScale=!0:(s=ui(this.Ge,this.gl),null!==s&&this.Bl(s))),1===t.ie&&t.ie!==i.ie&&(s=_i(this.Ge,this.gl),null!==s&&this.Bl(s));const n=i.ie!==this.Ps.mode;n&&(2===i.ie||this.Le())&&this.ga(),n&&(3===i.ie||this.Vl())&&this.ga(),void 0!==t.Il&&i.Il!==t.Il&&(this.Ps.invertScale=t.Il,this.El()),this.cl.p(i,this.ie())}Al(){return this.cl}P(){return this.bl.fontSize}$t(){return this.sl}zl(t){this.sl!==t&&(this.sl=t,this.Tl(),this.vl=null)}Ll(){if(this.nl)return this.nl;const t=this.$t()-this.Ol()-this.Nl();return this.nl=t,t}Qe(){return this.Fl(),this.Ge}Bl(t,i){const s=this.Ge;(i||null===s&&null!==t||null!==s&&!s.He(t))&&(this.vl=null,this.Ge=t)}Wl(t){this.Bl(t),this.Hl(null!==t)}Ki(){return this.Fl(),0===this.sl||!this.Ge||this.Ge.Ki()}Ul(t){return this.Il()?t:this.$t()-1-t}Nt(t,i){return this.Le()?t=ei(t,i):this.Vl()&&(t=hi(t,i)),this.kl(t,i)}$l(t,i,s){this.Fl();const n=this.Nl(),e=u(this.Qe()),r=e.$e(),h=e.qe(),a=this.Ll()-1,l=this.Il(),o=a/(h-r),_=void 0===s?0:s.from,c=void 0===s?t.length:s.to,d=this.ql();for(let s=_;s<c;s++){const e=t[s],h=e.gt;if(isNaN(h))continue;let a=h;null!==d&&(a=d(e.gt,i));const _=n+o*(a-r),u=l?_:this.sl-1-_;e.ut=u}}Yl(t,i,s){this.Fl();const n=this.Nl(),e=u(this.Qe()),r=e.$e(),h=e.qe(),a=this.Ll()-1,l=this.Il(),o=a/(h-r),_=void 0===s?0:s.from,c=void 0===s?t.length:s.to,d=this.ql();for(let s=_;s<c;s++){const e=t[s];let h=e.qh,a=e.Yh,_=e.jh,u=e.Kh;null!==d&&(h=d(e.qh,i),a=d(e.Yh,i),_=d(e.jh,i),u=d(e.Kh,i));let c=n+o*(h-r),f=l?c:this.sl-1-c;e.jl=f,c=n+o*(a-r),f=l?c:this.sl-1-c,e.Kl=f,c=n+o*(_-r),f=l?c:this.sl-1-c,e.Xl=f,c=n+o*(u-r),f=l?c:this.sl-1-c,e.Zl=f}}Ts(t,i){const s=this.Pl(t,i);return this.Gl(s,i)}Gl(t,i){let s=t;return this.Le()?s=function(t,i){return i<0&&(t=-t),t/100*i+i}(s,i):this.Vl()&&(s=function(t,i){return t-=100,i<0&&(t=-t),t/100*i+i}(s,i)),s}ba(){return this.dl}Dt(){return this.pl||(this.pl=pi(this.dl)),this.pl}Jl(t){-1===this.dl.indexOf(t)&&(this.dl.push(t),this.ga(),this.Ql())}io(t){const i=this.dl.indexOf(t);if(-1===i)throw new Error("source is not attached to scale");this.dl.splice(i,1),0===this.dl.length&&(this.yl({sn:!0}),this.Bl(null)),this.ga(),this.Ql()}zt(){let t=null;for(const i of this.dl){const s=i.zt();null!==s&&((null===t||s.Hh<t.Hh)&&(t=s))}return null===t?null:t.Wt}Il(){return this.Ps.invertScale}Va(){const t=null===this.zt();if(null!==this.vl&&(t||this.vl.so===t))return this.vl.Va;this.Cl.Ha();const i=this.Cl.Va();return this.vl={Va:i,so:t},this.ul.p(),i}no(){return this.ul}eo(t){this.Le()||this.Vl()||null===this.ml&&null===this.el&&(this.Ki()||(this.ml=this.sl-t,this.el=u(this.Qe()).Ue()))}ro(t){if(this.Le()||this.Vl())return;if(null===this.ml)return;this.yl({sn:!1}),(t=this.sl-t)<0&&(t=0);let i=(this.ml+.2*(this.sl-1))/(t+.2*(this.sl-1));const s=u(this.el).Ue();i=Math.max(i,.1),s.je(i),this.Bl(s)}ho(){this.Le()||this.Vl()||(this.ml=null,this.el=null)}ao(t){this.Rl()||null===this.wl&&null===this.el&&(this.Ki()||(this.wl=t,this.el=u(this.Qe()).Ue()))}lo(t){if(this.Rl())return;if(null===this.wl)return;const i=u(this.Qe()).Ye()/(this.Ll()-1);let s=t-this.wl;this.Il()&&(s*=-1);const n=s*i,e=u(this.el).Ue();e.Ke(n),this.Bl(e,!0),this.vl=null}oo(){this.Rl()||null!==this.wl&&(this.wl=null,this.el=null)}ra(){return this.ha||this.ga(),this.ha}Zi(t,i){switch(this.Ps.mode){case 2:return this._o(ei(t,i));case 3:return this.ra().format(hi(t,i));default:return this.nr(t)}}Ja(t){switch(this.Ps.mode){case 2:return this._o(t);case 3:return this.ra().format(t);default:return this.nr(t)}}Za(t){switch(this.Ps.mode){case 2:return this.uo(t);case 3:return this.ra().formatTickmarks(t);default:return this.co(t)}}Dh(t){return this.nr(t,u(this.fl).ra())}Vh(t,i){return t=ei(t,i),this._o(t,mi)}do(){return this.dl}fo(t){this.rl={al:t,hl:!1}}Ns(){this.dl.forEach((t=>t.Ns()))}qa(){return this.Ps.ensureEdgeTickMarksVisible&&this.Rl()}ja(){return this.P()/2}ga(){this.vl=null;let t=1/0;this.fl=null;for(const i of this.dl)i.hs()<t&&(t=i.hs(),this.fl=i);let i=100;null!==this.fl&&(i=Math.round(1/this.fl.ea())),this.ha=wi,this.Le()?(this.ha=mi,i=100):this.Vl()?(this.ha=new it(100,1),i=100):null!==this.fl&&(this.ha=this.fl.ra()),this.Cl=new fi(this,i,this.Pl.bind(this),this.kl.bind(this)),this.Cl.Ha()}Ql(){this.pl=null}Xi(){return this.xl}Hl(t){this.ll=t}Ol(){return this.Il()?this.Ps.scaleMargins.bottom*this.$t()+this._l:this.Ps.scaleMargins.top*this.$t()+this.ol}Nl(){return this.Il()?this.Ps.scaleMargins.top*this.$t()+this.ol:this.Ps.scaleMargins.bottom*this.$t()+this._l}Fl(){this.rl.hl||(this.rl.hl=!0,this.po())}Tl(){this.nl=null}kl(t,i){if(this.Fl(),this.Ki())return 0;t=this.Qa()&&t?li(t,this.gl):t;const s=u(this.Qe()),n=this.Nl()+(this.Ll()-1)*(t-s.$e())/s.Ye();return this.Ul(n)}Pl(t,i){if(this.Fl(),this.Ki())return 0;const s=this.Ul(t),n=u(this.Qe()),e=n.$e()+n.Ye()*((s-this.Nl())/(this.Ll()-1));return this.Qa()?oi(e,this.gl):e}El(){this.vl=null,this.Cl.Ha()}po(){if(this.Dl()&&!this.Rl())return;const t=this.rl.al;if(null===t)return;let i=null;const s=this.do();let n=0,e=0;for(const r of s){if(!r.Vt())continue;const s=r.zt();if(null===s)continue;const h=r.Mh(t.Uh(),t.bi());let a=h&&h.Qe();if(null!==a){switch(this.Ps.mode){case 1:a=_i(a,this.gl);break;case 2:a=ri(a,s.Wt);break;case 3:a=ai(a,s.Wt)}if(i=null===i?a:i.vn(u(a)),null!==h){const t=h.tr();null!==t&&(n=Math.max(n,t.above),e=Math.max(e,t.below))}}}if(this.qa()&&(n=Math.max(n,this.ja()),e=Math.max(e,this.ja())),n===this.ol&&e===this._l||(this.ol=n,this._l=e,this.vl=null,this.Tl()),null!==i){if(i.$e()===i.qe()){const t=this.fl,s=5*(null===t||this.Le()||this.Vl()?1:t.ea());this.Qa()&&(i=ui(i,this.gl)),i=new mt(i.$e()-s,i.qe()+s),this.Qa()&&(i=_i(i,this.gl))}if(this.Qa()){const t=ui(i,this.gl),s=ci(t);if(r=s,h=this.gl,r.Ia!==h.Ia||r.Ba!==h.Ba){const n=null!==this.el?ui(this.el,this.gl):null;this.gl=s,i=_i(t,s),null!==n&&(this.el=_i(n,s))}}this.Bl(i)}else null===this.Ge&&(this.Bl(new mt(-.5,.5)),this.gl=ci(null));var r,h}ql(){return this.Le()?ei:this.Vl()?hi:this.Qa()?t=>li(t,this.gl):null}vo(t,i,s){return void 0===i?(void 0===s&&(s=this.ra()),s.format(t)):i(t)}mo(t,i,s){return void 0===i?(void 0===s&&(s=this.ra()),s.formatTickmarks(t)):i(t)}nr(t,i){return this.vo(t,this.Sl.priceFormatter,i)}co(t,i){const s=this.Sl.priceFormatter;return this.mo(t,this.Sl.tickmarksPriceFormatter??(s?t=>t.map(s):void 0),i)}_o(t,i){return this.vo(t,this.Sl.percentageFormatter,i)}uo(t,i){const s=this.Sl.percentageFormatter;return this.mo(t,this.Sl.tickmarksPercentageFormatter??(s?t=>t.map(s):void 0),i)}}function Mi(t){return t instanceof jt}class bi{constructor(t,i){this.dl=[],this.wo=new Map,this.sl=0,this.Mo=0,this.bo=1,this.pl=null,this.So=!1,this.xo=new d,this.kh=[],this.uh=t,this.ts=i,this.Co=new si(this);const s=i.N();this.Po=this.ko("left",s.leftPriceScale),this.yo=this.ko("right",s.rightPriceScale),this.Po.Al().i(this.To.bind(this,this.Po),this),this.yo.Al().i(this.To.bind(this,this.yo),this),this.Ro(s)}Ro(t){if(t.leftPriceScale&&this.Po.hr(t.leftPriceScale),t.rightPriceScale&&this.yo.hr(t.rightPriceScale),t.localization&&(this.Po.ga(),this.yo.ga()),t.overlayPriceScales){const i=Array.from(this.wo.values());for(const s of i){const i=u(s[0].Ft());i.hr(t.overlayPriceScales),t.localization&&i.ga()}}}Do(t){switch(t){case"left":return this.Po;case"right":return this.yo}return this.wo.has(t)?_(this.wo.get(t))[0].Ft():null}m(){this.Qt().Vo().u(this),this.Po.Al().u(this),this.yo.Al().u(this),this.dl.forEach((t=>{t.m&&t.m()})),this.kh=this.kh.filter((t=>{const i=t.oh();return i.detached&&i.detached(),!1})),this.xo.p()}Io(){return this.bo}Bo(t){this.bo=t}Qt(){return this.ts}Qi(){return this.Mo}$t(){return this.sl}Eo(t){this.Mo=t,this.Ao()}zl(t){this.sl=t,this.Po.zl(t),this.yo.zl(t),this.dl.forEach((i=>{if(this.Un(i)){const s=i.Ft();null!==s&&s.zl(t)}})),this.Ao()}zo(t){this.So=t}Lo(){return this.So}Oo(){return this.dl.filter(Mi)}ba(){return this.dl}Un(t){const i=t.Ft();return null===i||this.Po!==i&&this.yo!==i}Jl(t,i,s){this.No(t,i,s?t.hs():this.dl.length)}io(t,i){const s=this.dl.indexOf(t);o(-1!==s,"removeDataSource: invalid data source"),this.dl.splice(s,1),i||this.dl.forEach(((t,i)=>t.ls(i)));const n=u(t.Ft()).wa();if(this.wo.has(n)){const i=_(this.wo.get(n)),s=i.indexOf(t);-1!==s&&(i.splice(s,1),0===i.length&&this.wo.delete(n))}const e=t.Ft();e&&e.ba().indexOf(t)>=0&&(e.io(t),this.Fo(e)),this.pl=null}qn(t){return t===this.Po?"left":t===this.yo?"right":"overlay"}Wo(){return this.Po}Ho(){return this.yo}Uo(t,i){t.eo(i)}$o(t,i){t.ro(i),this.Ao()}qo(t){t.ho()}Yo(t,i){t.ao(i)}jo(t,i){t.lo(i),this.Ao()}Ko(t){t.oo()}Ao(){this.dl.forEach((t=>{t.Ns()}))}ys(){let t=null;return this.ts.N().rightPriceScale.visible&&0!==this.yo.ba().length?t=this.yo:this.ts.N().leftPriceScale.visible&&0!==this.Po.ba().length?t=this.Po:0!==this.dl.length&&(t=this.dl[0].Ft()),null===t&&(t=this.yo),t}$n(){let t=null;return this.ts.N().rightPriceScale.visible?t=this.yo:this.ts.N().leftPriceScale.visible&&(t=this.Po),t}Fo(t){null!==t&&t.Rl()&&this.Xo(t)}Zo(t){const i=this.uh.Pe();t.yl({sn:!0}),null!==i&&t.fo(i),this.Ao()}Go(){this.Xo(this.Po),this.Xo(this.yo)}Jo(){this.Fo(this.Po),this.Fo(this.yo),this.dl.forEach((t=>{this.Un(t)&&this.Fo(t.Ft())})),this.Ao(),this.ts.ar()}Dt(){return null===this.pl&&(this.pl=pi(this.dl)),this.pl}Qo(t,i){i=Gt(i,0,this.dl.length-1);const s=this.dl.indexOf(t);o(-1!==s,"setSeriesOrder: invalid data source"),this.dl.splice(s,1),this.dl.splice(i,0,t),this.dl.forEach(((t,i)=>t.ls(i))),this.pl=null;for(const t of[this.Po,this.yo])t.Ql(),t.ga();this.ts.ar()}It(){return this.Dt().filter(Mi)}t_(){return this.xo}i_(){return this.Co}ua(t){this.kh.push(new At(t))}ca(t){this.kh=this.kh.filter((i=>i.oh()!==t)),t.detached&&t.detached(),this.ts.ar()}s_(){return this.kh}sa(t,i){return this.kh.map((s=>s.jn(t,i))).filter((t=>null!==t))}Xo(t){const i=t.do();if(i&&i.length>0&&!this.uh.Ki()){const i=this.uh.Pe();null!==i&&t.fo(i)}t.Ns()}No(t,i,s){let n=this.Do(i);if(null===n&&(n=this.ko(i,this.ts.N().overlayPriceScales)),this.dl.splice(s,0,t),!Z(i)){const s=this.wo.get(i)||[];s.push(t),this.wo.set(i,s)}t.ls(s),n.Jl(t),t._s(n),this.Fo(n),this.pl=null}To(t,i,s){i.ie!==s.ie&&this.Xo(t)}ko(t,i){const s={visible:!0,autoScale:!0,...g(i)},n=new gi(t,s,this.ts.N().layout,this.ts.N().localization,this.ts.Xi());return n.zl(this.$t()),n}}function Si(t){return{n_:t.n_,e_:{Kn:t.r_.externalId},h_:t.r_.cursorStyle}}function xi(t,i,s,n){for(const e of t){const t=e.Tt(n);if(null!==t&&t.jn){const n=t.jn(i,s);if(null!==n)return{a_:e,e_:n}}}return null}function Ci(t){return void 0!==t.Fs}function Pi(t,i,s){const n=[t,...t.Dt()],e=function(t,i,s){let n,e;for(const a of t){const t=a.sa?.(i,s)??[];for(const i of t)r=i.zOrder,h=n?.zOrder,(!h||"top"===r&&"top"!==h||"normal"===r&&"bottom"===h)&&(n=i,e=a)}var r,h;return n&&e?{r_:n,n_:e}:null}(n,i,s);if("top"===e?.r_.zOrder)return Si(e);for(const r of n){if(e&&e.n_===r&&"bottom"!==e.r_.zOrder&&!e.r_.isBackground)return Si(e);if(Ci(r)){const n=xi(r.Fs(t),i,s,t);if(null!==n)return{n_:r,a_:n.a_,e_:n.e_}}if(e&&e.n_===r&&"bottom"!==e.r_.zOrder&&e.r_.isBackground)return Si(e)}return e?.r_?Si(e):null}class ki{constructor(t,i,s=50){this.kn=0,this.yn=1,this.Tn=1,this.Dn=new Map,this.Rn=new Map,this.l_=t,this.o_=i,this.Vn=s}__(t){const i=t.time,s=this.o_.cacheKey(i),n=this.Dn.get(s);if(void 0!==n)return n.u_;if(this.kn===this.Vn){const t=this.Rn.get(this.Tn);this.Rn.delete(this.Tn),this.Dn.delete(_(t)),this.Tn++,this.kn--}const e=this.l_(t);return this.Dn.set(s,{u_:e,An:this.yn}),this.Rn.set(this.yn,s),this.kn++,this.yn++,e}}class yi{constructor(t,i){o(t<=i,"right should be >= left"),this.c_=t,this.d_=i}Uh(){return this.c_}bi(){return this.d_}f_(){return this.d_-this.c_+1}Te(t){return this.c_<=t&&t<=this.d_}He(t){return this.c_===t.Uh()&&this.d_===t.bi()}}function Ti(t,i){return null===t||null===i?t===i:t.He(i)}class Ri{constructor(){this.p_=new Map,this.Dn=null,this.v_=!1}m_(t){this.v_=t,this.Dn=null}w_(t,i){this.g_(i),this.Dn=null;for(let s=i;s<t.length;++s){const i=t[s];let n=this.p_.get(i.timeWeight);void 0===n&&(n=[],this.p_.set(i.timeWeight,n)),n.push({index:s,time:i.time,weight:i.timeWeight,originalTime:i.originalTime})}}M_(t,i,s,n,e){const r=Math.ceil(i/t);return null!==this.Dn&&this.Dn.b_===r&&e===this.Dn.S_&&s===this.Dn.x_||(this.Dn={S_:e,x_:s,Va:this.C_(r,s,n),b_:r}),this.Dn.Va}g_(t){if(0===t)return void this.p_.clear();const i=[];this.p_.forEach(((s,n)=>{t<=s[0].index?i.push(n):s.splice(kt(s,t,(i=>i.index<t)),1/0)}));for(const t of i)this.p_.delete(t)}C_(t,i,s){let n=[];const e=t=>!i||s.has(t.index);for(const i of Array.from(this.p_.keys()).sort(((t,i)=>i-t))){if(!this.p_.get(i))continue;const s=n;n=[];const r=s.length;let h=0;const a=_(this.p_.get(i)),l=a.length;let o=1/0,u=-1/0;for(let i=0;i<l;i++){const l=a[i],_=l.index;for(;h<r;){const t=s[h],i=t.index;if(!(i<_&&e(t))){o=i;break}h++,n.push(t),u=i,o=1/0}if(o-_>=t&&_-u>=t&&e(l))n.push(l),u=_;else if(this.v_)return s}for(;h<r;h++)e(s[h])&&n.push(s[h])}return n}}class Di{constructor(t){this.P_=t}k_(){return null===this.P_?null:new yi(Math.floor(this.P_.Uh()),Math.ceil(this.P_.bi()))}y_(){return this.P_}static T_(){return new Di(null)}}function Vi(t,i){return t.weight>i.weight?t:i}class Ii{constructor(t,i,s,n){this.Mo=0,this.R_=null,this.D_=[],this.wl=null,this.ml=null,this.V_=new Ri,this.I_=new Map,this.B_=Di.T_(),this.E_=!0,this.A_=new d,this.z_=new d,this.L_=new d,this.O_=null,this.N_=null,this.F_=new Map,this.W_=-1,this.H_=[],this.Ps=i,this.Sl=s,this.U_=i.rightOffset,this.q_=i.barSpacing,this.ts=t,this.o_=n,this.Y_(),this.V_.m_(i.uniformDistribution),this.j_()}N(){return this.Ps}K_(t){f(this.Sl,t),this.X_(),this.Y_()}hr(t,i){f(this.Ps,t),this.Ps.fixLeftEdge&&this.Z_(),this.Ps.fixRightEdge&&this.G_(),void 0!==t.barSpacing&&this.ts.dn(t.barSpacing),void 0!==t.rightOffset&&this.ts.fn(t.rightOffset),void 0===t.minBarSpacing&&void 0===t.maxBarSpacing||this.ts.dn(t.barSpacing??this.q_),void 0!==t.ignoreWhitespaceIndices&&t.ignoreWhitespaceIndices!==this.Ps.ignoreWhitespaceIndices&&this.j_(),this.X_(),this.Y_(),this.L_.p()}Rs(t){return this.D_[t]?.time??null}ss(t){return this.D_[t]??null}J_(t,i){if(this.D_.length<1)return null;if(this.o_.key(t)>this.o_.key(this.D_[this.D_.length-1].time))return i?this.D_.length-1:null;const s=kt(this.D_,this.o_.key(t),((t,i)=>this.o_.key(t.time)<i));return this.o_.key(t)<this.o_.key(this.D_[s].time)?i?s:null:s}Ki(){return 0===this.Mo||0===this.D_.length||null===this.R_}Q_(){return this.D_.length>0}Pe(){return this.tu(),this.B_.k_()}iu(){return this.tu(),this.B_.y_()}su(){const t=this.Pe();if(null===t)return null;const i={from:t.Uh(),to:t.bi()};return this.nu(i)}nu(t){const i=Math.round(t.from),s=Math.round(t.to),n=u(this.eu()),e=u(this.ru());return{from:u(this.ss(Math.max(n,i))),to:u(this.ss(Math.min(e,s)))}}hu(t){return{from:u(this.J_(t.from,!0)),to:u(this.J_(t.to,!0))}}Qi(){return this.Mo}Eo(t){if(!isFinite(t)||t<=0)return;if(this.Mo===t)return;const i=this.iu(),s=this.Mo;if(this.Mo=t,this.E_=!0,this.Ps.lockVisibleTimeRangeOnResize&&0!==s){const i=this.q_*t/s;this.q_=i}if(this.Ps.fixLeftEdge&&null!==i&&i.Uh()<=0){const i=s-t;this.U_-=Math.round(i/this.q_)+1,this.E_=!0}this.au(),this.lu()}qt(t){if(this.Ki()||!v(t))return 0;const i=this.ou()+this.U_-t;return this.Mo-(i+.5)*this.q_-1}_u(t,i){const s=this.ou(),n=void 0===i?0:i.from,e=void 0===i?t.length:i.to;for(let i=n;i<e;i++){const n=t[i].wt,e=s+this.U_-n,r=this.Mo-(e+.5)*this.q_-1;t[i]._t=r}}uu(t,i){const s=Math.ceil(this.cu(t));return i&&this.Ps.ignoreWhitespaceIndices&&!this.du(s)?this.fu(s):s}fn(t){this.E_=!0,this.U_=t,this.lu(),this.ts.pu(),this.ts.ar()}vu(){return this.q_}dn(t){this.mu(t),this.lu(),this.ts.pu(),this.ts.ar()}wu(){return this.U_}Va(){if(this.Ki())return null;if(null!==this.N_)return this.N_;const t=this.q_,i=5*(this.ts.N().layout.fontSize+4)/8*(this.Ps.tickMarkMaxCharacterLength||8),s=Math.round(i/t),n=u(this.Pe()),e=Math.max(n.Uh(),n.Uh()-s),r=Math.max(n.bi(),n.bi()-s),h=this.V_.M_(t,i,this.Ps.ignoreWhitespaceIndices,this.F_,this.W_),a=this.eu()+s,l=this.ru()-s,o=this.gu(),_=this.Ps.fixLeftEdge||o,c=this.Ps.fixRightEdge||o;let d=0;for(const t of h){if(!(e<=t.index&&t.index<=r))continue;let s;d<this.H_.length?(s=this.H_[d],s.coord=this.qt(t.index),s.label=this.Mu(t),s.weight=t.weight):(s={needAlignCoordinate:!1,coord:this.qt(t.index),label:this.Mu(t),weight:t.weight},this.H_.push(s)),this.q_>i/2&&!o?s.needAlignCoordinate=!1:s.needAlignCoordinate=_&&t.index<=a||c&&t.index>=l,d++}return this.H_.length=d,this.N_=this.H_,this.H_}bu(){this.E_=!0,this.dn(this.Ps.barSpacing),this.fn(this.Ps.rightOffset)}Su(t){this.E_=!0,this.R_=t,this.lu(),this.Z_()}xu(t,i){const s=this.cu(t),n=this.vu(),e=n+i*(n/10);this.dn(e),this.Ps.rightBarStaysOnScroll||this.fn(this.wu()+(s-this.cu(t)))}eo(t){this.wl&&this.oo(),null===this.ml&&null===this.O_&&(this.Ki()||(this.ml=t,this.Cu()))}ro(t){if(null===this.O_)return;const i=Gt(this.Mo-t,0,this.Mo),s=Gt(this.Mo-u(this.ml),0,this.Mo);0!==i&&0!==s&&this.dn(this.O_.vu*i/s)}ho(){null!==this.ml&&(this.ml=null,this.Pu())}ao(t){null===this.wl&&null===this.O_&&(this.Ki()||(this.wl=t,this.Cu()))}lo(t){if(null===this.wl)return;const i=(this.wl-t)/this.vu();this.U_=u(this.O_).wu+i,this.E_=!0,this.lu()}oo(){null!==this.wl&&(this.wl=null,this.Pu())}ku(){this.yu(this.Ps.rightOffset)}yu(t,i=400){if(!isFinite(t))throw new RangeError("offset is required and must be finite number");if(!isFinite(i)||i<=0)throw new RangeError("animationDuration (optional) must be finite positive number");const s=this.U_,n=performance.now();this.ts._n({Tu:t=>(t-n)/i>=1,Ru:e=>{const r=(e-n)/i;return r>=1?t:s+(t-s)*r}})}kt(t,i){this.E_=!0,this.D_=t,this.V_.w_(t,i),this.lu()}Du(){return this.A_}Vu(){return this.z_}Iu(){return this.L_}ou(){return this.R_||0}Bu(t){const i=t.f_();this.mu(this.Mo/i),this.U_=t.bi()-this.ou(),this.lu(),this.E_=!0,this.ts.pu(),this.ts.ar()}Eu(){const t=this.eu(),i=this.ru();null!==t&&null!==i&&this.Bu(new yi(t,i+this.Ps.rightOffset))}Au(t){const i=new yi(t.from,t.to);this.Bu(i)}ns(t){return void 0!==this.Sl.timeFormatter?this.Sl.timeFormatter(t.originalTime):this.o_.formatHorzItem(t.time)}j_(){if(!this.Ps.ignoreWhitespaceIndices)return;this.F_.clear();const t=this.ts.js();for(const i of t)for(const t of i.ma())this.F_.set(t,!0);this.W_++}gu(){const t=this.ts.N().handleScroll,i=this.ts.N().handleScale;return!(t.horzTouchDrag||t.mouseWheel||t.pressedMouseMove||t.vertTouchDrag||i.axisDoubleClickReset.time||i.axisPressedMouseMove.time||i.mouseWheel||i.pinch)}eu(){return 0===this.D_.length?null:0}ru(){return 0===this.D_.length?null:this.D_.length-1}zu(t){return(this.Mo-1-t)/this.q_}cu(t){const i=this.zu(t),s=this.ou()+this.U_-i;return Math.round(1e6*s)/1e6}mu(t){const i=this.q_;this.q_=t,this.au(),i!==this.q_&&(this.E_=!0,this.Lu())}tu(){if(!this.E_)return;if(this.E_=!1,this.Ki())return void this.Ou(Di.T_());const t=this.ou(),i=this.Mo/this.q_,s=this.U_+t,n=new yi(s-i+1,s);this.Ou(new Di(n))}au(){const t=Gt(this.q_,this.Nu(),this.Fu());this.q_!==t&&(this.q_=t,this.E_=!0)}Fu(){return this.Ps.maxBarSpacing>0?this.Ps.maxBarSpacing:.5*this.Mo}Nu(){return this.Ps.fixLeftEdge&&this.Ps.fixRightEdge&&0!==this.D_.length?this.Mo/this.D_.length:this.Ps.minBarSpacing}lu(){const t=this.Wu();null!==t&&this.U_<t&&(this.U_=t,this.E_=!0);const i=this.Hu();this.U_>i&&(this.U_=i,this.E_=!0)}Wu(){const t=this.eu(),i=this.R_;if(null===t||null===i)return null;return t-i-1+(this.Ps.fixLeftEdge?this.Mo/this.q_:Math.min(2,this.D_.length))}Hu(){return this.Ps.fixRightEdge?0:this.Mo/this.q_-Math.min(2,this.D_.length)}Cu(){this.O_={vu:this.vu(),wu:this.wu()}}Pu(){this.O_=null}Mu(t){let i=this.I_.get(t.weight);return void 0===i&&(i=new ki((t=>this.Uu(t)),this.o_),this.I_.set(t.weight,i)),i.__(t)}Uu(t){return this.o_.formatTickmark(t,this.Sl)}Ou(t){const i=this.B_;this.B_=t,Ti(i.k_(),this.B_.k_())||this.A_.p(),Ti(i.y_(),this.B_.y_())||this.z_.p(),this.Lu()}Lu(){this.N_=null}X_(){this.Lu(),this.I_.clear()}Y_(){this.o_.updateFormatter(this.Sl)}Z_(){if(!this.Ps.fixLeftEdge)return;const t=this.eu();if(null===t)return;const i=this.Pe();if(null===i)return;const s=i.Uh()-t;if(s<0){const t=this.U_-s-1;this.fn(t)}this.au()}G_(){this.lu(),this.au()}du(t){return!this.Ps.ignoreWhitespaceIndices||(this.F_.get(t)||!1)}fu(t){const i=function*(t){const i=Math.round(t),s=i<t;let n=1;for(;;)s?(yield i+n,yield i-n):(yield i-n,yield i+n),n++}(t),s=this.ru();for(;s;){const t=i.next().value;if(this.F_.get(t))return t;if(t<0||t>s)break}return t}}var Bi,Ei,Ai,zi,Li;!function(t){t[t.OnTouchEnd=0]="OnTouchEnd",t[t.OnNextTap=1]="OnNextTap"}(Bi||(Bi={}));class Oi{constructor(t,i,s){this.$u=[],this.qu=[],this.Mo=0,this.Yu=null,this.ju=new d,this.Ku=new d,this.Xu=null,this.Zu=t,this.Ps=i,this.o_=s,this.xl=new y(this.Ps.layout.colorParsers),this.Gu=new C(this),this.uh=new Ii(this,i.timeScale,this.Ps.localization,s),this.Ct=new X(this,i.crosshair),this.Ju=new Zt(i.crosshair),i.addDefaultPane&&(this.Qu(0),this.$u[0].Bo(2)),this.tc=this.sc(0),this.nc=this.sc(1)}Bh(){this.ec(G.gn())}ar(){this.ec(G.wn())}Zh(){this.ec(new G(1))}Eh(t){const i=this.rc(t);this.ec(i)}hc(){return this.Yu}ac(t){if(this.Yu?.n_===t?.n_&&this.Yu?.e_?.Kn===t?.e_?.Kn)return;const i=this.Yu;this.Yu=t,null!==i&&this.Eh(i.n_),null!==t&&t.n_!==i?.n_&&this.Eh(t.n_)}N(){return this.Ps}hr(t){f(this.Ps,t),this.$u.forEach((i=>i.Ro(t))),void 0!==t.timeScale&&this.uh.hr(t.timeScale),void 0!==t.localization&&this.uh.K_(t.localization),(t.leftPriceScale||t.rightPriceScale)&&this.ju.p(),this.tc=this.sc(0),this.nc=this.sc(1),this.Bh()}lc(t,i,s=0){const n=this.$u[s];if(void 0===n)return;if("left"===t)return f(this.Ps,{leftPriceScale:i}),n.Ro({leftPriceScale:i}),this.ju.p(),void this.Bh();if("right"===t)return f(this.Ps,{rightPriceScale:i}),n.Ro({rightPriceScale:i}),this.ju.p(),void this.Bh();const e=this.oc(t,s);null!==e&&(e.Ft.hr(i),this.ju.p())}oc(t,i){const s=this.$u[i];if(void 0===s)return null;const n=s.Do(t);return null!==n?{Us:s,Ft:n}:null}Et(){return this.uh}$s(){return this.$u}_c(){return this.Ct}uc(){return this.Ku}cc(t,i){t.zl(i),this.pu()}Eo(t){this.Mo=t,this.uh.Eo(this.Mo),this.$u.forEach((i=>i.Eo(t))),this.pu()}dc(t){1!==this.$u.length&&(o(t>=0&&t<this.$u.length,"Invalid pane index"),this.$u.splice(t,1),this.Bh())}fc(t,i){if(this.$u.length<2)return;o(t>=0&&t<this.$u.length,"Invalid pane index");const s=this.$u[t],n=this.$u.reduce(((t,i)=>t+i.Io()),0),e=this.$u.reduce(((t,i)=>t+i.$t()),0),r=e-30*(this.$u.length-1);i=Math.min(r,Math.max(30,i));const h=n/e,a=s.$t();s.Bo(i*h);let l=i-a,_=this.$u.length-1;for(const t of this.$u)if(t!==s){const i=Math.min(r,Math.max(30,t.$t()-l/_));l-=t.$t()-i,_-=1;const s=i*h;t.Bo(s)}this.Bh()}vc(t,i){o(t>=0&&t<this.$u.length&&i>=0&&i<this.$u.length,"Invalid pane index");const s=this.$u[t],n=this.$u[i];this.$u[t]=n,this.$u[i]=s,this.Bh()}mc(t,i){if(o(t>=0&&t<this.$u.length&&i>=0&&i<this.$u.length,"Invalid pane index"),t===i)return;const[s]=this.$u.splice(t,1);this.$u.splice(i,0,s),this.Bh()}Uo(t,i,s){t.Uo(i,s)}$o(t,i,s){t.$o(i,s),this.Ah(),this.ec(this.wc(t,2))}qo(t,i){t.qo(i),this.ec(this.wc(t,2))}Yo(t,i,s){i.Rl()||t.Yo(i,s)}jo(t,i,s){i.Rl()||(t.jo(i,s),this.Ah(),this.ec(this.wc(t,2)))}Ko(t,i){i.Rl()||(t.Ko(i),this.ec(this.wc(t,2)))}Zo(t,i){t.Zo(i),this.ec(this.wc(t,2))}gc(t){this.uh.eo(t)}Mc(t,i){const s=this.Et();if(s.Ki()||0===i)return;const n=s.Qi();t=Math.max(1,Math.min(t,n)),s.xu(t,i),this.pu()}bc(t){this.Sc(0),this.xc(t),this.Cc()}Pc(t){this.uh.ro(t),this.pu()}kc(){this.uh.ho(),this.ar()}Sc(t){this.uh.ao(t)}xc(t){this.uh.lo(t),this.pu()}Cc(){this.uh.oo(),this.ar()}js(){return this.qu}yc(t,i,s,n,e){this.Ct.Vs(t,i);let r=NaN,h=this.uh.uu(t,!0);const a=this.uh.Pe();null!==a&&(h=Math.min(Math.max(a.Uh(),h),a.bi()));const l=n.ys(),o=l.zt();if(null!==o&&(r=l.Ts(i,o)),r=this.Ju.Ma(r,h,n),this.Ct.As(h,r,n),this.Zh(),!e){const e=Pi(n,t,i);this.ac(e&&{n_:e.n_,e_:e.e_,h_:e.h_||null}),this.Ku.p(this.Ct.Bt(),{x:t,y:i},s)}}Tc(t,i,s){const n=s.ys(),e=n.zt(),r=n.Nt(t,u(e)),h=this.uh.J_(i,!0),a=this.uh.qt(u(h));this.yc(a,r,null,s,!0)}Rc(t){this._c().Ls(),this.Zh(),t||this.Ku.p(null,null,null)}Ah(){const t=this.Ct.Us();if(null!==t){const i=this.Ct.Bs(),s=this.Ct.Es();this.yc(i,s,null,t)}this.Ct.Ns()}Dc(t,i,s){const n=this.uh.Rs(0);void 0!==i&&void 0!==s&&this.uh.kt(i,s);const e=this.uh.Rs(0),r=this.uh.ou(),h=this.uh.Pe();if(null!==h&&null!==n&&null!==e){const i=h.Te(r),a=this.o_.key(n)>this.o_.key(e),l=null!==t&&t>r&&!a,o=this.uh.N().allowShiftVisibleRangeOnWhitespaceReplacement,_=i&&(!(void 0===s)||o)&&this.uh.N().shiftVisibleRangeOnNewBar;if(l&&!_){const i=t-r;this.uh.fn(this.uh.wu()-i)}}this.uh.Su(t)}Lh(t){null!==t&&t.Jo()}Hn(t){if(function(t){return t instanceof bi}(t))return t;const i=this.$u.find((i=>i.Dt().includes(t)));return void 0===i?null:i}pu(){this.$u.forEach((t=>t.Jo())),this.Ah()}m(){this.$u.forEach((t=>t.m())),this.$u.length=0,this.Ps.localization.priceFormatter=void 0,this.Ps.localization.percentageFormatter=void 0,this.Ps.localization.timeFormatter=void 0}Vc(){return this.Gu}Yn(){return this.Gu.N()}Vo(){return this.ju}Ic(t,i){const s=this.Qu(i);this.Bc(t,s),this.qu.push(t),1===this.qu.length?this.Bh():this.ar()}Ec(t){const i=this.Hn(t),s=this.qu.indexOf(t);o(-1!==s,"Series not found");const n=u(i);this.qu.splice(s,1),n.io(t),t.m&&t.m(),this.uh.j_(),this.Ac(n)}Ih(t,i){const s=u(this.Hn(t));s.io(t,!0),s.Jl(t,i,!0)}Eu(){const t=G.wn();t.rn(),this.ec(t)}zc(t){const i=G.wn();i.ln(t),this.ec(i)}cn(){const t=G.wn();t.cn(),this.ec(t)}dn(t){const i=G.wn();i.dn(t),this.ec(i)}fn(t){const i=G.wn();i.fn(t),this.ec(i)}_n(t){const i=G.wn();i._n(t),this.ec(i)}hn(){const t=G.wn();t.hn(),this.ec(t)}Lc(){return this.Ps.rightPriceScale.visible?"right":"left"}Oc(t,i){o(i>=0,"Index should be greater or equal to 0");if(i===this.Nc(t))return;const s=u(this.Hn(t));s.io(t);const n=this.Qu(i);this.Bc(t,n),0===s.ba().length&&this.Ac(s),this.Bh()}Fc(){return this.nc}$(){return this.tc}Ut(t){const i=this.nc,s=this.tc;if(i===s)return i;if(t=Math.max(0,Math.min(100,Math.round(100*t))),null===this.Xu||this.Xu.mr!==s||this.Xu.wr!==i)this.Xu={mr:s,wr:i,Wc:new Map};else{const i=this.Xu.Wc.get(t);if(void 0!==i)return i}const n=this.xl.tt(s,i,t/100);return this.Xu.Wc.set(t,n),n}Hc(t){return this.$u.indexOf(t)}Xi(){return this.xl}Uc(){return this.$c()}$c(t){const i=new bi(this.uh,this);this.$u.push(i);const s=t??this.$u.length-1,n=G.gn();return n.Qs(s,{tn:0,sn:!0}),this.ec(n),i}Qu(t){return o(t>=0,"Index should be greater or equal to 0"),(t=Math.min(this.$u.length,t))<this.$u.length?this.$u[t]:this.$c(t)}Nc(t){return this.$u.findIndex((i=>i.Oo().includes(t)))}wc(t,i){const s=new G(i);if(null!==t){const n=this.$u.indexOf(t);s.Qs(n,{tn:i})}return s}rc(t,i){return void 0===i&&(i=2),this.wc(this.Hn(t),i)}ec(t){this.Zu&&this.Zu(t),this.$u.forEach((t=>t.i_().lr().kt()))}Bc(t,i){const s=t.N().priceScaleId,n=void 0!==s?s:this.Lc();i.Jl(t,n),Z(n)||t.hr(t.N())}sc(t){const i=this.Ps.layout;return"gradient"===i.background.type?0===t?i.background.topColor:i.background.bottomColor:i.background.color}Ac(t){!t.Lo()&&0===t.ba().length&&this.$u.length>1&&this.$u.splice(this.Hc(t),1)}}function Ni(t){return!p(t)&&!m(t)}function Fi(t){return p(t)}!function(t){t[t.Disabled=0]="Disabled",t[t.Continuous=1]="Continuous",t[t.OnDataUpdate=2]="OnDataUpdate"}(Ei||(Ei={})),function(t){t[t.LastBar=0]="LastBar",t[t.LastVisible=1]="LastVisible"}(Ai||(Ai={})),function(t){t.Solid="solid",t.VerticalGradient="gradient"}(zi||(zi={})),function(t){t[t.Year=0]="Year",t[t.Month=1]="Month",t[t.DayOfMonth=2]="DayOfMonth",t[t.Time=3]="Time",t[t.TimeWithSeconds=4]="TimeWithSeconds"}(Li||(Li={}));const Wi=t=>t.getUTCFullYear();function Hi(t,i,s){return i.replace(/yyyy/g,(t=>tt(Wi(t),4))(t)).replace(/yy/g,(t=>tt(Wi(t)%100,2))(t)).replace(/MMMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"long"}))(t,s)).replace(/MMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"short"}))(t,s)).replace(/MM/g,(t=>tt((t=>t.getUTCMonth()+1)(t),2))(t)).replace(/dd/g,(t=>tt((t=>t.getUTCDate())(t),2))(t))}class Ui{constructor(t="yyyy-MM-dd",i="default"){this.qc=t,this.Yc=i}__(t){return Hi(t,this.qc,this.Yc)}}class $i{constructor(t){this.jc=t||"%h:%m:%s"}__(t){return this.jc.replace("%h",tt(t.getUTCHours(),2)).replace("%m",tt(t.getUTCMinutes(),2)).replace("%s",tt(t.getUTCSeconds(),2))}}const qi={Kc:"yyyy-MM-dd",Xc:"%h:%m:%s",Zc:" ",Gc:"default"};class Yi{constructor(t={}){const i={...qi,...t};this.Jc=new Ui(i.Kc,i.Gc),this.Qc=new $i(i.Xc),this.td=i.Zc}__(t){return`${this.Jc.__(t)}${this.td}${this.Qc.__(t)}`}}function ji(t){return 60*t*60*1e3}function Ki(t){return 60*t*1e3}const Xi=[{sd:(Zi=1,1e3*Zi),nd:10},{sd:Ki(1),nd:20},{sd:Ki(5),nd:21},{sd:Ki(30),nd:22},{sd:ji(1),nd:30},{sd:ji(3),nd:31},{sd:ji(6),nd:32},{sd:ji(12),nd:33}];var Zi;function Gi(t,i){if(t.getUTCFullYear()!==i.getUTCFullYear())return 70;if(t.getUTCMonth()!==i.getUTCMonth())return 60;if(t.getUTCDate()!==i.getUTCDate())return 50;for(let s=Xi.length-1;s>=0;--s)if(Math.floor(i.getTime()/Xi[s].sd)!==Math.floor(t.getTime()/Xi[s].sd))return Xi[s].nd;return 0}function Ji(t){let i=t;if(m(t)&&(i=ts(t)),!Ni(i))throw new Error("time must be of type BusinessDay");const s=new Date(Date.UTC(i.year,i.month-1,i.day,0,0,0,0));return{ed:Math.round(s.getTime()/1e3),rd:i}}function Qi(t){if(!Fi(t))throw new Error("time must be of type isUTCTimestamp");return{ed:t}}function ts(t){const i=new Date(t);if(isNaN(i.getTime()))throw new Error(`Invalid date string=${t}, expected format=yyyy-mm-dd`);return{day:i.getUTCDate(),month:i.getUTCMonth()+1,year:i.getUTCFullYear()}}function is(t){m(t.time)&&(t.time=ts(t.time))}class ss{options(){return this.Ps}setOptions(t){this.Ps=t,this.updateFormatter(t.localization)}preprocessData(t){Array.isArray(t)?function(t){t.forEach(is)}(t):is(t)}createConverterToInternalObj(t){return u(function(t){return 0===t.length?null:Ni(t[0].time)||m(t[0].time)?Ji:Qi}(t))}key(t){return"object"==typeof t&&"ed"in t?t.ed:this.key(this.convertHorzItemToInternal(t))}cacheKey(t){const i=t;return void 0===i.rd?new Date(1e3*i.ed).getTime():new Date(Date.UTC(i.rd.year,i.rd.month-1,i.rd.day)).getTime()}convertHorzItemToInternal(t){return Fi(i=t)?Qi(i):Ni(i)?Ji(i):Ji(ts(i));var i}updateFormatter(t){if(!this.Ps)return;const i=t.dateFormat;this.Ps.timeScale.timeVisible?this.hd=new Yi({Kc:i,Xc:this.Ps.timeScale.secondsVisible?"%h:%m:%s":"%h:%m",Zc:"   ",Gc:t.locale}):this.hd=new Ui(i,t.locale)}formatHorzItem(t){const i=t;return this.hd.__(new Date(1e3*i.ed))}formatTickmark(t,i){const s=function(t,i,s){switch(t){case 0:case 10:return i?s?4:3:2;case 20:case 21:case 22:case 30:case 31:case 32:case 33:return i?3:2;case 50:return 2;case 60:return 1;case 70:return 0}}(t.weight,this.Ps.timeScale.timeVisible,this.Ps.timeScale.secondsVisible),n=this.Ps.timeScale;if(void 0!==n.tickMarkFormatter){const e=n.tickMarkFormatter(t.originalTime,s,i.locale);if(null!==e)return e}return function(t,i,s){const n={};switch(i){case 0:n.year="numeric";break;case 1:n.month="short";break;case 2:n.day="numeric";break;case 3:n.hour12=!1,n.hour="2-digit",n.minute="2-digit";break;case 4:n.hour12=!1,n.hour="2-digit",n.minute="2-digit",n.second="2-digit"}const e=void 0===t.rd?new Date(1e3*t.ed):new Date(Date.UTC(t.rd.year,t.rd.month-1,t.rd.day));return new Date(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()).toLocaleString(s,n)}(t.time,s,i.locale)}maxTickMarkWeight(t){let i=t.reduce(Vi,t[0]).weight;return i>30&&i<50&&(i=30),i}fillWeightsForPoints(t,i){!function(t,i=0){if(0===t.length)return;let s=0===i?null:t[i-1].time.ed,n=null!==s?new Date(1e3*s):null,e=0;for(let r=i;r<t.length;++r){const i=t[r],h=new Date(1e3*i.time.ed);null!==n&&(i.timeWeight=Gi(h,n)),e+=i.time.ed-(s||i.time.ed),s=i.time.ed,n=h}if(0===i&&t.length>1){const i=Math.ceil(e/(t.length-1)),s=new Date(1e3*(t[0].time.ed-i));t[0].timeWeight=Gi(new Date(1e3*t[0].time.ed),s)}}(t,i)}static ad(t){return f({localization:{dateFormat:"dd MMM 'yy"}},t??{})}}const ns="undefined"!=typeof window;function es(){return!!ns&&window.navigator.userAgent.toLowerCase().indexOf("firefox")>-1}function rs(){return!!ns&&/iPhone|iPad|iPod/.test(window.navigator.platform)}function hs(t){return t+t%2}function as(t){ns&&void 0!==window.chrome&&t.addEventListener("mousedown",(t=>{if(1===t.button)return t.preventDefault(),!1}))}class ls{constructor(t,i,s){this.ld=0,this.od=null,this._d={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY},this.ud=0,this.dd=null,this.fd={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY},this.pd=null,this.vd=!1,this.md=null,this.wd=null,this.gd=!1,this.Md=!1,this.bd=!1,this.Sd=null,this.xd=null,this.Cd=null,this.Pd=null,this.kd=null,this.yd=null,this.Td=null,this.Rd=0,this.Dd=!1,this.Vd=!1,this.Id=!1,this.Bd=0,this.Ed=null,this.Ad=!rs(),this.zd=t=>{this.Ld(t)},this.Od=t=>{if(this.Nd(t)){const i=this.Fd(t);if(++this.ud,this.dd&&this.ud>1){const{Wd:s}=this.Hd(us(t),this.fd);s<30&&!this.bd&&this.Ud(i,this.qd.$d),this.Yd()}}else{const i=this.Fd(t);if(++this.ld,this.od&&this.ld>1){const{Wd:s}=this.Hd(us(t),this._d);s<5&&!this.Md&&this.jd(i,this.qd.Kd),this.Xd()}}},this.Zd=t,this.qd=i,this.Ps=s,this.Gd()}m(){null!==this.Sd&&(this.Sd(),this.Sd=null),null!==this.xd&&(this.xd(),this.xd=null),null!==this.Pd&&(this.Pd(),this.Pd=null),null!==this.kd&&(this.kd(),this.kd=null),null!==this.yd&&(this.yd(),this.yd=null),null!==this.Cd&&(this.Cd(),this.Cd=null),this.Jd(),this.Xd()}Qd(t){this.Pd&&this.Pd();const i=this.tf.bind(this);if(this.Pd=()=>{this.Zd.removeEventListener("mousemove",i)},this.Zd.addEventListener("mousemove",i),this.Nd(t))return;const s=this.Fd(t);this.jd(s,this.qd.if),this.Ad=!0}Xd(){null!==this.od&&clearTimeout(this.od),this.ld=0,this.od=null,this._d={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY}}Yd(){null!==this.dd&&clearTimeout(this.dd),this.ud=0,this.dd=null,this.fd={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY}}tf(t){if(this.Id||null!==this.wd)return;if(this.Nd(t))return;const i=this.Fd(t);this.jd(i,this.qd.sf),this.Ad=!0}nf(t){const i=ds(t.changedTouches,u(this.Ed));if(null===i)return;if(this.Bd=cs(t),null!==this.Td)return;if(this.Vd)return;this.Dd=!0;const s=this.Hd(us(i),u(this.wd)),{ef:n,rf:e,Wd:r}=s;if(this.gd||!(r<5)){if(!this.gd){const t=.5*n,i=e>=t&&!this.Ps.hf(),s=t>e&&!this.Ps.af();i||s||(this.Vd=!0),this.gd=!0,this.bd=!0,this.Jd(),this.Yd()}if(!this.Vd){const s=this.Fd(t,i);this.Ud(s,this.qd.lf),_s(t)}}}_f(t){if(0!==t.button)return;const i=this.Hd(us(t),u(this.md)),{Wd:s}=i;if(s>=5&&(this.Md=!0,this.Xd()),this.Md){const i=this.Fd(t);this.jd(i,this.qd.uf)}}Hd(t,i){const s=Math.abs(i._t-t._t),n=Math.abs(i.ut-t.ut);return{ef:s,rf:n,Wd:s+n}}cf(t){let i=ds(t.changedTouches,u(this.Ed));if(null===i&&0===t.touches.length&&(i=t.changedTouches[0]),null===i)return;this.Ed=null,this.Bd=cs(t),this.Jd(),this.wd=null,this.yd&&(this.yd(),this.yd=null);const s=this.Fd(t,i);if(this.Ud(s,this.qd.df),++this.ud,this.dd&&this.ud>1){const{Wd:t}=this.Hd(us(i),this.fd);t<30&&!this.bd&&this.Ud(s,this.qd.$d),this.Yd()}else this.bd||(this.Ud(s,this.qd.ff),this.qd.ff&&_s(t));0===this.ud&&_s(t),0===t.touches.length&&this.vd&&(this.vd=!1,_s(t))}Ld(t){if(0!==t.button)return;const i=this.Fd(t);if(this.md=null,this.Id=!1,this.kd&&(this.kd(),this.kd=null),es()){this.Zd.ownerDocument.documentElement.removeEventListener("mouseleave",this.zd)}if(!this.Nd(t))if(this.jd(i,this.qd.pf),++this.ld,this.od&&this.ld>1){const{Wd:s}=this.Hd(us(t),this._d);s<5&&!this.Md&&this.jd(i,this.qd.Kd),this.Xd()}else this.Md||this.jd(i,this.qd.vf)}Jd(){null!==this.pd&&(clearTimeout(this.pd),this.pd=null)}mf(t){if(null!==this.Ed)return;const i=t.changedTouches[0];this.Ed=i.identifier,this.Bd=cs(t);const s=this.Zd.ownerDocument.documentElement;this.bd=!1,this.gd=!1,this.Vd=!1,this.wd=us(i),this.yd&&(this.yd(),this.yd=null);{const i=this.nf.bind(this),n=this.cf.bind(this);this.yd=()=>{s.removeEventListener("touchmove",i),s.removeEventListener("touchend",n)},s.addEventListener("touchmove",i,{passive:!1}),s.addEventListener("touchend",n,{passive:!1}),this.Jd(),this.pd=setTimeout(this.wf.bind(this,t),240)}const n=this.Fd(t,i);this.Ud(n,this.qd.gf),this.dd||(this.ud=0,this.dd=setTimeout(this.Yd.bind(this),500),this.fd=us(i))}Mf(t){if(0!==t.button)return;const i=this.Zd.ownerDocument.documentElement;es()&&i.addEventListener("mouseleave",this.zd),this.Md=!1,this.md=us(t),this.kd&&(this.kd(),this.kd=null);{const t=this._f.bind(this),s=this.Ld.bind(this);this.kd=()=>{i.removeEventListener("mousemove",t),i.removeEventListener("mouseup",s)},i.addEventListener("mousemove",t),i.addEventListener("mouseup",s)}if(this.Id=!0,this.Nd(t))return;const s=this.Fd(t);this.jd(s,this.qd.bf),this.od||(this.ld=0,this.od=setTimeout(this.Xd.bind(this),500),this._d=us(t))}Gd(){this.Zd.addEventListener("mouseenter",this.Qd.bind(this)),this.Zd.addEventListener("touchcancel",this.Jd.bind(this));{const t=this.Zd.ownerDocument,i=t=>{this.qd.Sf&&(t.composed&&this.Zd.contains(t.composedPath()[0])||t.target&&this.Zd.contains(t.target)||this.qd.Sf())};this.xd=()=>{t.removeEventListener("touchstart",i)},this.Sd=()=>{t.removeEventListener("mousedown",i)},t.addEventListener("mousedown",i),t.addEventListener("touchstart",i,{passive:!0})}rs()&&(this.Cd=()=>{this.Zd.removeEventListener("dblclick",this.Od)},this.Zd.addEventListener("dblclick",this.Od)),this.Zd.addEventListener("mouseleave",this.xf.bind(this)),this.Zd.addEventListener("touchstart",this.mf.bind(this),{passive:!0}),as(this.Zd),this.Zd.addEventListener("mousedown",this.Mf.bind(this)),this.Cf(),this.Zd.addEventListener("touchmove",(()=>{}),{passive:!1})}Cf(){void 0===this.qd.Pf&&void 0===this.qd.kf&&void 0===this.qd.yf||(this.Zd.addEventListener("touchstart",(t=>this.Tf(t.touches)),{passive:!0}),this.Zd.addEventListener("touchmove",(t=>{if(2===t.touches.length&&null!==this.Td&&void 0!==this.qd.kf){const i=os(t.touches[0],t.touches[1])/this.Rd;this.qd.kf(this.Td,i),_s(t)}}),{passive:!1}),this.Zd.addEventListener("touchend",(t=>{this.Tf(t.touches)})))}Tf(t){1===t.length&&(this.Dd=!1),2!==t.length||this.Dd||this.vd?this.Rf():this.Df(t)}Df(t){const i=this.Zd.getBoundingClientRect()||{left:0,top:0};this.Td={_t:(t[0].clientX-i.left+(t[1].clientX-i.left))/2,ut:(t[0].clientY-i.top+(t[1].clientY-i.top))/2},this.Rd=os(t[0],t[1]),void 0!==this.qd.Pf&&this.qd.Pf(),this.Jd()}Rf(){null!==this.Td&&(this.Td=null,void 0!==this.qd.yf&&this.qd.yf())}xf(t){if(this.Pd&&this.Pd(),this.Nd(t))return;if(!this.Ad)return;const i=this.Fd(t);this.jd(i,this.qd.Vf),this.Ad=!rs()}wf(t){const i=ds(t.touches,u(this.Ed));if(null===i)return;const s=this.Fd(t,i);this.Ud(s,this.qd.If),this.bd=!0,this.vd=!0}Nd(t){return t.sourceCapabilities&&void 0!==t.sourceCapabilities.firesTouchEvents?t.sourceCapabilities.firesTouchEvents:cs(t)<this.Bd+500}Ud(t,i){i&&i.call(this.qd,t)}jd(t,i){i&&i.call(this.qd,t)}Fd(t,i){const s=i||t,n=this.Zd.getBoundingClientRect()||{left:0,top:0};return{clientX:s.clientX,clientY:s.clientY,pageX:s.pageX,pageY:s.pageY,screenX:s.screenX,screenY:s.screenY,localX:s.clientX-n.left,localY:s.clientY-n.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,Bf:!t.type.startsWith("mouse")&&"contextmenu"!==t.type&&"click"!==t.type,Ef:t.type,Af:s.target,a_:t.view,zf:()=>{"touchstart"!==t.type&&_s(t)}}}}function os(t,i){const s=t.clientX-i.clientX,n=t.clientY-i.clientY;return Math.sqrt(s*s+n*n)}function _s(t){t.cancelable&&t.preventDefault()}function us(t){return{_t:t.pageX,ut:t.pageY}}function cs(t){return t.timeStamp||performance.now()}function ds(t,i){for(let s=0;s<t.length;++s)if(t[s].identifier===i)return t[s];return null}class fs{constructor(t,i,s){this.Lf=null,this.Of=null,this.Nf=!0,this.Ff=null,this.Wf=t,this.Hf=t.Uf()[i],this.$f=t.Uf()[s],this.qf=document.createElement("tr"),this.qf.style.height="1px",this.Yf=document.createElement("td"),this.Yf.style.position="relative",this.Yf.style.padding="0",this.Yf.style.margin="0",this.Yf.setAttribute("colspan","3"),this.jf(),this.qf.appendChild(this.Yf),this.Nf=this.Wf.N().layout.panes.enableResize,this.Nf?this.Kf():(this.Lf=null,this.Of=null)}m(){null!==this.Of&&this.Of.m()}Xf(){return this.qf}Zf(){return t({width:this.Hf.Zf().width,height:1})}Gf(){return t({width:this.Hf.Gf().width,height:1*window.devicePixelRatio})}Jf(t,i,s){const n=this.Gf();t.fillStyle=this.Wf.N().layout.panes.separatorColor,t.fillRect(i,s,n.width,n.height)}kt(){this.jf(),this.Wf.N().layout.panes.enableResize!==this.Nf&&(this.Nf=this.Wf.N().layout.panes.enableResize,this.Nf?this.Kf():(null!==this.Lf&&(this.Yf.removeChild(this.Lf.Qf),this.Yf.removeChild(this.Lf.tp),this.Lf=null),null!==this.Of&&(this.Of.m(),this.Of=null)))}Kf(){const t=document.createElement("div"),i=t.style;i.position="fixed",i.display="none",i.zIndex="49",i.top="0",i.left="0",i.width="100%",i.height="100%",i.cursor="row-resize",this.Yf.appendChild(t);const s=document.createElement("div"),n=s.style;n.position="absolute",n.zIndex="50",n.top="-4px",n.height="9px",n.width="100%",n.backgroundColor="",n.cursor="row-resize",this.Yf.appendChild(s);const e={if:this.ip.bind(this),Vf:this.sp.bind(this),bf:this.np.bind(this),gf:this.np.bind(this),uf:this.ep.bind(this),lf:this.ep.bind(this),pf:this.rp.bind(this),df:this.rp.bind(this)};this.Of=new ls(s,e,{hf:()=>!1,af:()=>!0}),this.Lf={tp:s,Qf:t}}jf(){this.Yf.style.background=this.Wf.N().layout.panes.separatorColor}ip(t){null!==this.Lf&&(this.Lf.tp.style.backgroundColor=this.Wf.N().layout.panes.separatorHoverColor)}sp(t){null!==this.Lf&&null===this.Ff&&(this.Lf.tp.style.backgroundColor="")}np(t){if(null===this.Lf)return;const i=this.Hf.hp().Io()+this.$f.hp().Io(),s=i/(this.Hf.Zf().height+this.$f.Zf().height),n=30*s;i<=2*n||(this.Ff={ap:t.pageY,lp:this.Hf.hp().Io(),op:i-n,_p:i,up:s,cp:n},this.Lf.Qf.style.display="block")}ep(t){const i=this.Ff;if(null===i)return;const s=(t.pageY-i.ap)*i.up,n=Gt(i.lp+s,i.cp,i.op);this.Hf.hp().Bo(n),this.$f.hp().Bo(i._p-n),this.Wf.Qt().Bh()}rp(t){null!==this.Ff&&null!==this.Lf&&(this.Ff=null,this.Lf.Qf.style.display="none")}}function ps(t,i){return t.dp-i.dp}function vs(t,i,s){const n=(t.dp-i.dp)/(t.wt-i.wt);return Math.sign(n)*Math.min(Math.abs(n),s)}class ms{constructor(t,i,s,n){this.fp=null,this.pp=null,this.vp=null,this.mp=null,this.wp=null,this.gp=0,this.Mp=0,this.bp=t,this.Sp=i,this.xp=s,this.Mn=n}Cp(t,i){if(null!==this.fp){if(this.fp.wt===i)return void(this.fp.dp=t);if(Math.abs(this.fp.dp-t)<this.Mn)return}this.mp=this.vp,this.vp=this.pp,this.pp=this.fp,this.fp={wt:i,dp:t}}le(t,i){if(null===this.fp||null===this.pp)return;if(i-this.fp.wt>50)return;let s=0;const n=vs(this.fp,this.pp,this.Sp),e=ps(this.fp,this.pp),r=[n],h=[e];if(s+=e,null!==this.vp){const t=vs(this.pp,this.vp,this.Sp);if(Math.sign(t)===Math.sign(n)){const i=ps(this.pp,this.vp);if(r.push(t),h.push(i),s+=i,null!==this.mp){const t=vs(this.vp,this.mp,this.Sp);if(Math.sign(t)===Math.sign(n)){const i=ps(this.vp,this.mp);r.push(t),h.push(i),s+=i}}}}let a=0;for(let t=0;t<r.length;++t)a+=h[t]/s*r[t];Math.abs(a)<this.bp||(this.wp={dp:t,wt:i},this.Mp=a,this.gp=function(t,i){const s=Math.log(i);return Math.log(1*s/-t)/s}(Math.abs(a),this.xp))}Ru(t){const i=u(this.wp),s=t-i.wt;return i.dp+this.Mp*(Math.pow(this.xp,s)-1)/Math.log(this.xp)}Tu(t){return null===this.wp||this.Pp(t)===this.gp}Pp(t){const i=t-u(this.wp).wt;return Math.min(i,this.gp)}}class ws{constructor(t,i){this.kp=void 0,this.yp=void 0,this.Tp=void 0,this.ps=!1,this.Rp=t,this.Dp=i,this.Vp()}kt(){this.Vp()}Ip(){this.kp&&this.Rp.removeChild(this.kp),this.yp&&this.Rp.removeChild(this.yp),this.kp=void 0,this.yp=void 0}Bp(){return this.ps!==this.Ep()||this.Tp!==this.Ap()}Ap(){return this.Dp.Qt().Xi().J(this.Dp.N().layout.textColor)>160?"dark":"light"}Ep(){return this.Dp.N().layout.attributionLogo}zp(){const t=new URL(location.href);return t.hostname?"&utm_source="+t.hostname+t.pathname:""}Vp(){this.Bp()&&(this.Ip(),this.ps=this.Ep(),this.ps&&(this.Tp=this.Ap(),this.yp=document.createElement("style"),this.yp.innerText="a#tv-attr-logo{--fill:#131722;--stroke:#fff;position:absolute;left:10px;bottom:10px;height:19px;width:35px;margin:0;padding:0;border:0;z-index:3;}a#tv-attr-logo[data-dark]{--fill:#D1D4DC;--stroke:#131722;}",this.kp=document.createElement("a"),this.kp.href=`https://www.tradingview.com/?utm_medium=lwc-link&utm_campaign=lwc-chart${this.zp()}`,this.kp.title="Charting by TradingView",this.kp.id="tv-attr-logo",this.kp.target="_blank",this.kp.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="35" height="19" fill="none"><g fill-rule="evenodd" clip-path="url(#a)" clip-rule="evenodd"><path fill="var(--stroke)" d="M2 0H0v10h6v9h21.4l.5-1.3 6-15 1-2.7H23.7l-.5 1.3-.2.6a5 5 0 0 0-7-.9V0H2Zm20 17h4l5.2-13 .8-2h-7l-1 2.5-.2.5-1.5 3.8-.3.7V17Zm-.8-10a3 3 0 0 0 .7-2.7A3 3 0 1 0 16.8 7h4.4ZM14 7V2H2v6h6v9h4V7h2Z"/><path fill="var(--fill)" d="M14 2H2v6h6v9h6V2Zm12 15h-7l6-15h7l-6 15Zm-7-9a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></g><defs><clipPath id="a"><path fill="var(--stroke)" d="M0 0h35v19H0z"/></clipPath></defs></svg>',this.kp.toggleAttribute("data-dark","dark"===this.Tp),this.Rp.appendChild(this.yp),this.Rp.appendChild(this.kp)))}}function gs(t,s){const n=u(t.ownerDocument).createElement("canvas");t.appendChild(n);const e=i(n,{type:"device-pixel-content-box",options:{allowResizeObserver:!0},transform:(t,i)=>({width:Math.max(t.width,i.width),height:Math.max(t.height,i.height)})});return e.resizeCanvasElement(s),e}function Ms(t){t.width=1,t.height=1,t.getContext("2d")?.clearRect(0,0,1,1)}function bs(t,i,s,n){t.ih&&t.ih(i,s,n)}function Ss(t,i,s,n){t.nt(i,s,n)}function xs(t,i,s,n){const e=t(s,n);for(const t of e){const s=t.Tt(n);null!==s&&i(s)}}function Cs(t,i){return s=>{if(!function(t){return void 0!==t.Ft}(s))return[];return(s.Ft()?.wa()??"")!==i?[]:s.ta?.(t)??[]}}function Ps(t,i,s,n){if(!t.length)return;let e=0;const r=t[0].$t(n,!0);let h=1===i?s/2-(t[0].Fi()-r/2):t[0].Fi()-r/2-s/2;h=Math.max(0,h);for(let r=1;r<t.length;r++){const a=t[r],l=t[r-1],o=l.$t(n,!1),_=a.Fi(),u=l.Fi();if(1===i?_>u-o:_<u+o){const n=u-o*i;a.Wi(n);const r=n-i*o/2;if((1===i?r<0:r>s)&&h>0){const n=1===i?-1-r:r-s,a=Math.min(n,h);for(let s=e;s<t.length;s++)t[s].Wi(t[s].Fi()+i*a);h-=a}}else e=r,h=1===i?u-o-_:_-(u+o)}}class ks{constructor(i,s,n,e){this.Yi=null,this.Lp=null,this.Op=!1,this.Np=new rt(200),this.Fp=null,this.Wp=0,this.Hp=!1,this.Up=()=>{this.Hp||this.Pt.$p().Qt().ar()},this.qp=()=>{this.Hp||this.Pt.$p().Qt().ar()},this.Pt=i,this.Ps=s,this.bl=s.layout,this.Gu=n,this.Yp="left"===e,this.jp=Cs("normal",e),this.Kp=Cs("top",e),this.Xp=Cs("bottom",e),this.Yf=document.createElement("div"),this.Yf.style.height="100%",this.Yf.style.overflow="hidden",this.Yf.style.width="25px",this.Yf.style.left="0",this.Yf.style.position="relative",this.Zp=gs(this.Yf,t({width:16,height:16})),this.Zp.subscribeSuggestedBitmapSizeChanged(this.Up);const r=this.Zp.canvasElement;r.style.position="absolute",r.style.zIndex="1",r.style.left="0",r.style.top="0",this.Gp=gs(this.Yf,t({width:16,height:16})),this.Gp.subscribeSuggestedBitmapSizeChanged(this.qp);const h=this.Gp.canvasElement;h.style.position="absolute",h.style.zIndex="2",h.style.left="0",h.style.top="0";const a={bf:this.np.bind(this),gf:this.np.bind(this),uf:this.ep.bind(this),lf:this.ep.bind(this),Sf:this.Jp.bind(this),pf:this.rp.bind(this),df:this.rp.bind(this),Kd:this.Qp.bind(this),$d:this.Qp.bind(this),if:this.tv.bind(this),Vf:this.sp.bind(this)};this.Of=new ls(this.Gp.canvasElement,a,{hf:()=>!this.Ps.handleScroll.vertTouchDrag,af:()=>!0})}m(){this.Of.m(),this.Gp.unsubscribeSuggestedBitmapSizeChanged(this.qp),Ms(this.Gp.canvasElement),this.Gp.dispose(),this.Zp.unsubscribeSuggestedBitmapSizeChanged(this.Up),Ms(this.Zp.canvasElement),this.Zp.dispose(),null!==this.Yi&&this.Yi.no().u(this),this.Yi=null}Xf(){return this.Yf}P(){return this.bl.fontSize}iv(){const t=this.Gu.N();return this.Fp!==t.k&&(this.Np.In(),this.Fp=t.k),t}sv(){if(null===this.Yi)return 0;let t=0;const i=this.iv(),s=u(this.Zp.canvasElement.getContext("2d",{colorSpace:this.Pt.$p().N().layout.colorSpace}));s.save();const n=this.Yi.Va();s.font=this.nv(),n.length>0&&(t=Math.max(this.Np.Vi(s,n[0].Ga),this.Np.Vi(s,n[n.length-1].Ga)));const e=this.ev();for(let i=e.length;i--;){const n=this.Np.Vi(s,e[i].ri());n>t&&(t=n)}const r=this.Yi.zt();if(null!==r&&null!==this.Lp&&(2!==(h=this.Ps.crosshair).mode&&h.horzLine.visible&&h.horzLine.labelVisible)){const i=this.Yi.Ts(1,r),n=this.Yi.Ts(this.Lp.height-2,r);t=Math.max(t,this.Np.Vi(s,this.Yi.Zi(Math.floor(Math.min(i,n))+.11111111111111,r)),this.Np.Vi(s,this.Yi.Zi(Math.ceil(Math.max(i,n))-.11111111111111,r)))}var h;s.restore();const a=t||34;return hs(Math.ceil(i.S+i.C+i.I+i.B+5+a))}rv(t){null!==this.Lp&&s(this.Lp,t)||(this.Lp=t,this.Hp=!0,this.Zp.resizeCanvasElement(t),this.Gp.resizeCanvasElement(t),this.Hp=!1,this.Yf.style.width=`${t.width}px`,this.Yf.style.height=`${t.height}px`)}hv(){return u(this.Lp).width}_s(t){this.Yi!==t&&(null!==this.Yi&&this.Yi.no().u(this),this.Yi=t,t.no().i(this.ul.bind(this),this))}Ft(){return this.Yi}In(){const t=this.Pt.hp();this.Pt.$p().Qt().Zo(t,u(this.Ft()))}av(t){if(null===this.Lp)return;const i={colorSpace:this.Pt.$p().N().layout.colorSpace};if(1!==t){this.lv(),this.Zp.applySuggestedBitmapSize();const t=n(this.Zp,i);null!==t&&(t.useBitmapCoordinateSpace((t=>{this.ov(t),this._v(t)})),this.Pt.uv(t,this.Xp),this.cv(t),this.Pt.uv(t,this.jp),this.dv(t))}this.Gp.applySuggestedBitmapSize();const s=n(this.Gp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.fv(s),this.Pt.uv(s,this.Kp))}Gf(){return this.Zp.bitmapSize}Jf(t,i,s){const n=this.Gf();n.width>0&&n.height>0&&t.drawImage(this.Zp.canvasElement,i,s)}kt(){this.Yi?.Va()}np(t){if(null===this.Yi||this.Yi.Ki()||!this.Ps.handleScale.axisPressedMouseMove.price)return;const i=this.Pt.$p().Qt(),s=this.Pt.hp();this.Op=!0,i.Uo(s,this.Yi,t.localY)}ep(t){if(null===this.Yi||!this.Ps.handleScale.axisPressedMouseMove.price)return;const i=this.Pt.$p().Qt(),s=this.Pt.hp(),n=this.Yi;i.$o(s,n,t.localY)}Jp(){if(null===this.Yi||!this.Ps.handleScale.axisPressedMouseMove.price)return;const t=this.Pt.$p().Qt(),i=this.Pt.hp(),s=this.Yi;this.Op&&(this.Op=!1,t.qo(i,s))}rp(t){if(null===this.Yi||!this.Ps.handleScale.axisPressedMouseMove.price)return;const i=this.Pt.$p().Qt(),s=this.Pt.hp();this.Op=!1,i.qo(s,this.Yi)}Qp(t){this.Ps.handleScale.axisDoubleClickReset.price&&this.In()}tv(t){if(null===this.Yi)return;!this.Pt.$p().Qt().N().handleScale.axisPressedMouseMove.price||this.Yi.Le()||this.Yi.Vl()||this.pv(1)}sp(t){this.pv(0)}ev(){const t=[],i=null===this.Yi?void 0:this.Yi;return(s=>{for(let n=0;n<s.length;++n){const e=s[n].Ws(this.Pt.hp(),i);for(let i=0;i<e.length;i++)t.push(e[i])}})(this.Pt.hp().Dt()),t}ov({context:t,bitmapSize:i}){const{width:s,height:n}=i,e=this.Pt.hp().Qt(),r=e.$(),h=e.Fc();r===h?L(t,0,0,s,n,r):F(t,0,0,s,n,r,h)}_v({context:t,bitmapSize:i,horizontalPixelRatio:s}){if(null===this.Lp||null===this.Yi||!this.Yi.N().borderVisible)return;t.fillStyle=this.Yi.N().borderColor;const n=Math.max(1,Math.floor(this.iv().S*s));let e;e=this.Yp?i.width-n:0,t.fillRect(e,0,n,i.height)}cv(t){if(null===this.Lp||null===this.Yi)return;const i=this.Yi.Va(),s=this.Yi.N(),n=this.iv(),e=this.Yp?this.Lp.width-n.C:0;s.borderVisible&&s.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:r,verticalPixelRatio:h})=>{t.fillStyle=s.borderColor;const a=Math.max(1,Math.floor(h)),l=Math.floor(.5*h),o=Math.round(n.C*r);t.beginPath();for(const s of i)t.rect(Math.floor(e*r),Math.round(s.ka*h)-l,o,a);t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{t.font=this.nv(),t.fillStyle=s.textColor??this.bl.textColor,t.textAlign=this.Yp?"right":"left",t.textBaseline="middle";const r=this.Yp?Math.round(e-n.I):Math.round(e+n.C+n.I),h=i.map((i=>this.Np.Di(t,i.Ga)));for(let s=i.length;s--;){const n=i[s];t.fillText(n.Ga,r,n.ka+h[s])}}))}lv(){if(null===this.Lp||null===this.Yi)return;let t=this.Lp.height/2;const i=[],s=this.Yi.Dt().slice(),n=this.Pt.hp(),e=this.iv();this.Yi===n.$n()&&this.Pt.hp().Dt().forEach((t=>{n.Un(t)&&s.push(t)}));const r=this.Yi.ba()[0],h=this.Yi;s.forEach((s=>{const e=s.Ws(n,h);e.forEach((t=>{t.Wi(null),t.Hi()&&i.push(t)})),r===s&&e.length>0&&(t=e[0].Bi())})),i.forEach((t=>t.Wi(t.Bi())));this.Yi.N().alignLabels&&this.vv(i,e,t)}vv(t,i,s){if(null===this.Lp)return;const n=t.filter((t=>t.Bi()<=s)),e=t.filter((t=>t.Bi()>s));n.sort(((t,i)=>i.Bi()-t.Bi())),n.length&&e.length&&e.push(n[0]),e.sort(((t,i)=>t.Bi()-i.Bi()));for(const s of t){const t=Math.floor(s.$t(i)/2),n=s.Bi();n>-t&&n<t&&s.Wi(t),n>this.Lp.height-t&&n<this.Lp.height+t&&s.Wi(this.Lp.height-t)}Ps(n,1,this.Lp.height,i),Ps(e,-1,this.Lp.height,i)}dv(t){if(null===this.Lp)return;const i=this.ev(),s=this.iv(),n=this.Yp?"right":"left";i.forEach((i=>{if(i.Ui()){i.Tt(u(this.Yi)).nt(t,s,this.Np,n)}}))}fv(t){if(null===this.Lp||null===this.Yi)return;const i=this.Pt.$p().Qt(),s=[],n=this.Pt.hp(),e=i._c().Ws(n,this.Yi);e.length&&s.push(e);const r=this.iv(),h=this.Yp?"right":"left";s.forEach((i=>{i.forEach((i=>{i.Tt(u(this.Yi)).nt(t,r,this.Np,h)}))}))}pv(t){this.Yf.style.cursor=1===t?"ns-resize":"default"}ul(){const t=this.sv();this.Wp<t&&this.Pt.$p().Qt().Bh(),this.Wp=t}nv(){return x(this.bl.fontSize,this.bl.fontFamily)}}function ys(t,i){return t.Jh?.(i)??[]}function Ts(t,i){return t.Fs?.(i)??[]}function Rs(t,i){return t.us?.(i)??[]}function Ds(t,i){return t.Xh?.(i)??[]}class Vs{constructor(i,s){this.Lp=t({width:0,height:0}),this.mv=null,this.wv=null,this.gv=null,this.Mv=null,this.bv=!1,this.Sv=new d,this.xv=new d,this.Cv=0,this.Pv=!1,this.kv=null,this.yv=!1,this.Tv=null,this.Rv=null,this.Hp=!1,this.Up=()=>{this.Hp||null===this.Dv||this.ts().ar()},this.qp=()=>{this.Hp||null===this.Dv||this.ts().ar()},this.Dp=i,this.Dv=s,this.Dv.t_().i(this.Vv.bind(this),this,!0),this.Iv=document.createElement("td"),this.Iv.style.padding="0",this.Iv.style.position="relative";const n=document.createElement("div");n.style.width="100%",n.style.height="100%",n.style.position="relative",n.style.overflow="hidden",this.Bv=document.createElement("td"),this.Bv.style.padding="0",this.Ev=document.createElement("td"),this.Ev.style.padding="0",this.Iv.appendChild(n),this.Zp=gs(n,t({width:16,height:16})),this.Zp.subscribeSuggestedBitmapSizeChanged(this.Up);const e=this.Zp.canvasElement;e.style.position="absolute",e.style.zIndex="1",e.style.left="0",e.style.top="0",this.Gp=gs(n,t({width:16,height:16})),this.Gp.subscribeSuggestedBitmapSizeChanged(this.qp);const r=this.Gp.canvasElement;r.style.position="absolute",r.style.zIndex="2",r.style.left="0",r.style.top="0",this.qf=document.createElement("tr"),this.qf.appendChild(this.Bv),this.qf.appendChild(this.Iv),this.qf.appendChild(this.Ev),this.Av(),this.Of=new ls(this.Gp.canvasElement,this,{hf:()=>null===this.kv&&!this.Dp.N().handleScroll.vertTouchDrag,af:()=>null===this.kv&&!this.Dp.N().handleScroll.horzTouchDrag})}m(){null!==this.mv&&this.mv.m(),null!==this.wv&&this.wv.m(),this.gv=null,this.Gp.unsubscribeSuggestedBitmapSizeChanged(this.qp),Ms(this.Gp.canvasElement),this.Gp.dispose(),this.Zp.unsubscribeSuggestedBitmapSizeChanged(this.Up),Ms(this.Zp.canvasElement),this.Zp.dispose(),null!==this.Dv&&(this.Dv.t_().u(this),this.Dv.m()),this.Of.m()}hp(){return u(this.Dv)}zv(t){null!==this.Dv&&this.Dv.t_().u(this),this.Dv=t,null!==this.Dv&&this.Dv.t_().i(Vs.prototype.Vv.bind(this),this,!0),this.Av(),this.Dp.Uf().indexOf(this)===this.Dp.Uf().length-1?(this.gv=this.gv??new ws(this.Iv,this.Dp),this.gv.kt()):(this.gv?.Ip(),this.gv=null)}$p(){return this.Dp}Xf(){return this.qf}Av(){if(null!==this.Dv&&(this.Lv(),0!==this.ts().js().length)){if(null!==this.mv){const t=this.Dv.Wo();this.mv._s(u(t))}if(null!==this.wv){const t=this.Dv.Ho();this.wv._s(u(t))}}}Ov(){null!==this.mv&&this.mv.kt(),null!==this.wv&&this.wv.kt()}Io(){return null!==this.Dv?this.Dv.Io():0}Bo(t){this.Dv&&this.Dv.Bo(t)}if(t){if(!this.Dv)return;this.Nv();const i=t.localX,s=t.localY;this.Fv(i,s,t)}bf(t){this.Nv(),this.Wv(),this.Fv(t.localX,t.localY,t)}sf(t){if(!this.Dv)return;this.Nv();const i=t.localX,s=t.localY;this.Fv(i,s,t)}vf(t){null!==this.Dv&&(this.Nv(),this.Hv(t))}Kd(t){null!==this.Dv&&this.Uv(this.xv,t)}$d(t){this.Kd(t)}uf(t){this.Nv(),this.$v(t),this.Fv(t.localX,t.localY,t)}pf(t){null!==this.Dv&&(this.Nv(),this.Pv=!1,this.qv(t))}ff(t){null!==this.Dv&&this.Hv(t)}If(t){if(this.Pv=!0,null===this.kv){const i={x:t.localX,y:t.localY};this.Yv(i,i,t)}}Vf(t){null!==this.Dv&&(this.Nv(),this.Dv.Qt().ac(null),this.jv())}Kv(){return this.Sv}Xv(){return this.xv}Pf(){this.Cv=1,this.ts().hn()}kf(t,i){if(!this.Dp.N().handleScale.pinch)return;const s=5*(i-this.Cv);this.Cv=i,this.ts().Mc(t._t,s)}gf(t){this.Pv=!1,this.yv=null!==this.kv,this.Wv();const i=this.ts()._c();null!==this.kv&&i.Vt()&&(this.Tv={x:i.si(),y:i.ni()},this.kv={x:t.localX,y:t.localY})}lf(t){if(null===this.Dv)return;const i=t.localX,s=t.localY;if(null===this.kv)this.$v(t);else{this.yv=!1;const n=u(this.Tv),e=n.x+(i-this.kv.x),r=n.y+(s-this.kv.y);this.Fv(e,r,t)}}df(t){0===this.$p().N().trackingMode.exitMode&&(this.yv=!0),this.Zv(),this.qv(t)}jn(t,i){const s=this.Dv;return null===s?null:Pi(s,t,i)}Gv(i,s){u("left"===s?this.mv:this.wv).rv(t({width:i,height:this.Lp.height}))}Zf(){return this.Lp}rv(t){s(this.Lp,t)||(this.Lp=t,this.Hp=!0,this.Zp.resizeCanvasElement(t),this.Gp.resizeCanvasElement(t),this.Hp=!1,this.Iv.style.width=t.width+"px",this.Iv.style.height=t.height+"px")}Jv(){const t=u(this.Dv);t.Fo(t.Wo()),t.Fo(t.Ho());for(const i of t.ba())if(t.Un(i)){const s=i.Ft();null!==s&&t.Fo(s),i.Ns()}for(const i of t.s_())i.Ns()}Gf(){return this.Zp.bitmapSize}Jf(t,i,s){const n=this.Gf();n.width>0&&n.height>0&&t.drawImage(this.Zp.canvasElement,i,s)}av(t){if(0===t)return;if(null===this.Dv)return;t>1&&this.Jv(),null!==this.mv&&this.mv.av(t),null!==this.wv&&this.wv.av(t);const i={colorSpace:this.Dp.N().layout.colorSpace};if(1!==t){this.Zp.applySuggestedBitmapSize();const t=n(this.Zp,i);null!==t&&(t.useBitmapCoordinateSpace((t=>{this.ov(t)})),this.Dv&&(this.Qv(t,ys),this.tm(t),this.Qv(t,Ts),this.Qv(t,Rs)))}this.Gp.applySuggestedBitmapSize();const s=n(this.Gp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.im(s),this.Qv(s,Ds),this.Qv(s,Rs))}sm(){return this.mv}nm(){return this.wv}uv(t,i){this.Qv(t,i)}Vv(){null!==this.Dv&&this.Dv.t_().u(this),this.Dv=null}Hv(t){this.Uv(this.Sv,t)}Uv(t,i){const s=i.localX,n=i.localY;t.v()&&t.p(this.ts().Et().uu(s),{x:s,y:n},i)}ov({context:t,bitmapSize:i}){const{width:s,height:n}=i,e=this.ts(),r=e.$(),h=e.Fc();r===h?L(t,0,0,s,n,h):F(t,0,0,s,n,r,h)}tm(t){const i=u(this.Dv),s=i.i_().lr().Tt(i);null!==s&&s.nt(t,!1)}im(t){this.rm(t,Ts,Ss,this.ts()._c())}Qv(t,i){const s=u(this.Dv),n=s.Dt(),e=s.s_();for(const s of e)this.rm(t,i,bs,s);for(const s of n)this.rm(t,i,bs,s);for(const s of e)this.rm(t,i,Ss,s);for(const s of n)this.rm(t,i,Ss,s)}rm(t,i,s,n){const e=u(this.Dv),r=e.Qt().hc(),h=null!==r&&r.n_===n,a=null!==r&&h&&void 0!==r.e_?r.e_.Xn:void 0;xs(i,(i=>s(i,t,h,a)),n,e)}Lv(){if(null===this.Dv)return;const t=this.Dp,i=this.Dv.Wo().N().visible,s=this.Dv.Ho().N().visible;i||null===this.mv||(this.Bv.removeChild(this.mv.Xf()),this.mv.m(),this.mv=null),s||null===this.wv||(this.Ev.removeChild(this.wv.Xf()),this.wv.m(),this.wv=null);const n=t.Qt().Vc();i&&null===this.mv&&(this.mv=new ks(this,t.N(),n,"left"),this.Bv.appendChild(this.mv.Xf())),s&&null===this.wv&&(this.wv=new ks(this,t.N(),n,"right"),this.Ev.appendChild(this.wv.Xf()))}hm(t){return t.Bf&&this.Pv||null!==this.kv}am(t){return Math.max(0,Math.min(t,this.Lp.width-1))}lm(t){return Math.max(0,Math.min(t,this.Lp.height-1))}Fv(t,i,s){this.ts().yc(this.am(t),this.lm(i),s,u(this.Dv))}jv(){this.ts().Rc()}Zv(){this.yv&&(this.kv=null,this.jv())}Yv(t,i,s){this.kv=t,this.yv=!1,this.Fv(i.x,i.y,s);const n=this.ts()._c();this.Tv={x:n.si(),y:n.ni()}}ts(){return this.Dp.Qt()}qv(t){if(!this.bv)return;const i=this.ts(),s=this.hp();if(i.Ko(s,s.ys()),this.Mv=null,this.bv=!1,i.Cc(),null!==this.Rv){const t=performance.now(),s=i.Et();this.Rv.le(s.wu(),t),this.Rv.Tu(t)||i._n(this.Rv)}}Nv(){this.kv=null}Wv(){if(!this.Dv)return;if(this.ts().hn(),document.activeElement!==document.body&&document.activeElement!==document.documentElement)u(document.activeElement).blur();else{const t=document.getSelection();null!==t&&t.removeAllRanges()}!this.Dv.ys().Ki()&&this.ts().Et().Ki()}$v(t){if(null===this.Dv)return;const i=this.ts(),s=i.Et();if(s.Ki())return;const n=this.Dp.N(),e=n.handleScroll,r=n.kineticScroll;if((!e.pressedMouseMove||t.Bf)&&(!e.horzTouchDrag&&!e.vertTouchDrag||!t.Bf))return;const h=this.Dv.ys(),a=performance.now();if(null!==this.Mv||this.hm(t)||(this.Mv={x:t.clientX,y:t.clientY,ed:a,om:t.localX,_m:t.localY}),null!==this.Mv&&!this.bv&&(this.Mv.x!==t.clientX||this.Mv.y!==t.clientY)){if(t.Bf&&r.touch||!t.Bf&&r.mouse){const t=s.vu();this.Rv=new ms(.2/t,7/t,.997,15/t),this.Rv.Cp(s.wu(),this.Mv.ed)}else this.Rv=null;h.Ki()||i.Yo(this.Dv,h,t.localY),i.Sc(t.localX),this.bv=!0}this.bv&&(h.Ki()||i.jo(this.Dv,h,t.localY),i.xc(t.localX),null!==this.Rv&&this.Rv.Cp(s.wu(),a))}}class Is{constructor(i,s,n,e,r){this.xt=!0,this.Lp=t({width:0,height:0}),this.Up=()=>this.av(3),this.Yp="left"===i,this.Gu=n.Vc,this.Ps=s,this.um=e,this.dm=r,this.Yf=document.createElement("div"),this.Yf.style.width="25px",this.Yf.style.height="100%",this.Yf.style.overflow="hidden",this.Zp=gs(this.Yf,t({width:16,height:16})),this.Zp.subscribeSuggestedBitmapSizeChanged(this.Up)}m(){this.Zp.unsubscribeSuggestedBitmapSizeChanged(this.Up),Ms(this.Zp.canvasElement),this.Zp.dispose()}Xf(){return this.Yf}Zf(){return this.Lp}rv(t){s(this.Lp,t)||(this.Lp=t,this.Zp.resizeCanvasElement(t),this.Yf.style.width=`${t.width}px`,this.Yf.style.height=`${t.height}px`,this.xt=!0)}av(t){if(t<3&&!this.xt)return;if(0===this.Lp.width||0===this.Lp.height)return;this.xt=!1,this.Zp.applySuggestedBitmapSize();const i=n(this.Zp,{colorSpace:this.Ps.layout.colorSpace});null!==i&&i.useBitmapCoordinateSpace((t=>{this.ov(t),this._v(t)}))}Gf(){return this.Zp.bitmapSize}Jf(t,i,s){const n=this.Gf();n.width>0&&n.height>0&&t.drawImage(this.Zp.canvasElement,i,s)}_v({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(!this.um())return;t.fillStyle=this.Ps.timeScale.borderColor;const e=Math.floor(this.Gu.N().S*s),r=Math.floor(this.Gu.N().S*n),h=this.Yp?i.width-e:0;t.fillRect(h,0,e,r)}ov({context:t,bitmapSize:i}){L(t,0,0,i.width,i.height,this.dm())}}function Bs(t){return i=>i.ia?.(t)??[]}const Es=Bs("normal"),As=Bs("top"),zs=Bs("bottom");class Ls{constructor(i,s){this.fm=null,this.pm=null,this.M=null,this.vm=!1,this.Lp=t({width:0,height:0}),this.wm=new d,this.Np=new rt(5),this.Hp=!1,this.Up=()=>{this.Hp||this.Dp.Qt().ar()},this.qp=()=>{this.Hp||this.Dp.Qt().ar()},this.Dp=i,this.o_=s,this.Ps=i.N().layout,this.kp=document.createElement("tr"),this.gm=document.createElement("td"),this.gm.style.padding="0",this.Mm=document.createElement("td"),this.Mm.style.padding="0",this.Yf=document.createElement("td"),this.Yf.style.height="25px",this.Yf.style.padding="0",this.bm=document.createElement("div"),this.bm.style.width="100%",this.bm.style.height="100%",this.bm.style.position="relative",this.bm.style.overflow="hidden",this.Yf.appendChild(this.bm),this.Zp=gs(this.bm,t({width:16,height:16})),this.Zp.subscribeSuggestedBitmapSizeChanged(this.Up);const n=this.Zp.canvasElement;n.style.position="absolute",n.style.zIndex="1",n.style.left="0",n.style.top="0",this.Gp=gs(this.bm,t({width:16,height:16})),this.Gp.subscribeSuggestedBitmapSizeChanged(this.qp);const e=this.Gp.canvasElement;e.style.position="absolute",e.style.zIndex="2",e.style.left="0",e.style.top="0",this.kp.appendChild(this.gm),this.kp.appendChild(this.Yf),this.kp.appendChild(this.Mm),this.Sm(),this.Dp.Qt().Vo().i(this.Sm.bind(this),this),this.Of=new ls(this.Gp.canvasElement,this,{hf:()=>!0,af:()=>!this.Dp.N().handleScroll.horzTouchDrag})}m(){this.Of.m(),null!==this.fm&&this.fm.m(),null!==this.pm&&this.pm.m(),this.Gp.unsubscribeSuggestedBitmapSizeChanged(this.qp),Ms(this.Gp.canvasElement),this.Gp.dispose(),this.Zp.unsubscribeSuggestedBitmapSizeChanged(this.Up),Ms(this.Zp.canvasElement),this.Zp.dispose()}Xf(){return this.kp}xm(){return this.fm}Cm(){return this.pm}bf(t){if(this.vm)return;this.vm=!0;const i=this.Dp.Qt();!i.Et().Ki()&&this.Dp.N().handleScale.axisPressedMouseMove.time&&i.gc(t.localX)}gf(t){this.bf(t)}Sf(){const t=this.Dp.Qt();!t.Et().Ki()&&this.vm&&(this.vm=!1,this.Dp.N().handleScale.axisPressedMouseMove.time&&t.kc())}uf(t){const i=this.Dp.Qt();!i.Et().Ki()&&this.Dp.N().handleScale.axisPressedMouseMove.time&&i.Pc(t.localX)}lf(t){this.uf(t)}pf(){this.vm=!1;const t=this.Dp.Qt();t.Et().Ki()&&!this.Dp.N().handleScale.axisPressedMouseMove.time||t.kc()}df(){this.pf()}Kd(){this.Dp.N().handleScale.axisDoubleClickReset.time&&this.Dp.Qt().cn()}$d(){this.Kd()}if(){this.Dp.Qt().N().handleScale.axisPressedMouseMove.time&&this.pv(1)}Vf(){this.pv(0)}Zf(){return this.Lp}Pm(){return this.wm}km(i,n,e){s(this.Lp,i)||(this.Lp=i,this.Hp=!0,this.Zp.resizeCanvasElement(i),this.Gp.resizeCanvasElement(i),this.Hp=!1,this.Yf.style.width=`${i.width}px`,this.Yf.style.height=`${i.height}px`,this.wm.p(i)),null!==this.fm&&this.fm.rv(t({width:n,height:i.height})),null!==this.pm&&this.pm.rv(t({width:e,height:i.height}))}ym(){const t=this.Tm();return Math.ceil(t.S+t.C+t.P+t.A+t.V+t.Rm)}kt(){this.Dp.Qt().Et().Va()}Gf(){return this.Zp.bitmapSize}Jf(t,i,s){const n=this.Gf();n.width>0&&n.height>0&&t.drawImage(this.Zp.canvasElement,i,s)}av(t){if(0===t)return;const i={colorSpace:this.Ps.colorSpace};if(1!==t){this.Zp.applySuggestedBitmapSize();const s=n(this.Zp,i);null!==s&&(s.useBitmapCoordinateSpace((t=>{this.ov(t),this._v(t),this.Dm(s,zs)})),this.cv(s),this.Dm(s,Es)),null!==this.fm&&this.fm.av(t),null!==this.pm&&this.pm.av(t)}this.Gp.applySuggestedBitmapSize();const s=n(this.Gp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.Vm([...this.Dp.Qt().js(),this.Dp.Qt()._c()],s),this.Dm(s,As))}Dm(t,i){const s=this.Dp.Qt().js();for(const n of s)xs(i,(i=>bs(i,t,!1,void 0)),n,void 0);for(const n of s)xs(i,(i=>Ss(i,t,!1,void 0)),n,void 0)}ov({context:t,bitmapSize:i}){L(t,0,0,i.width,i.height,this.Dp.Qt().Fc())}_v({context:t,bitmapSize:i,verticalPixelRatio:s}){if(this.Dp.N().timeScale.borderVisible){t.fillStyle=this.Im();const n=Math.max(1,Math.floor(this.Tm().S*s));t.fillRect(0,0,i.width,n)}}cv(t){const i=this.Dp.Qt().Et(),s=i.Va();if(!s||0===s.length)return;const n=this.o_.maxTickMarkWeight(s),e=this.Tm(),r=i.N();r.borderVisible&&r.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:i,verticalPixelRatio:n})=>{t.strokeStyle=this.Im(),t.fillStyle=this.Im();const r=Math.max(1,Math.floor(i)),h=Math.floor(.5*i);t.beginPath();const a=Math.round(e.C*n);for(let n=s.length;n--;){const e=Math.round(s[n].coord*i);t.rect(e-h,0,r,a)}t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{const i=e.S+e.C+e.A+e.P/2;t.textAlign="center",t.textBaseline="middle",t.fillStyle=this.H(),t.font=this.nv();for(const e of s)if(e.weight<n){const s=e.needAlignCoordinate?this.Bm(t,e.coord,e.label):e.coord;t.fillText(e.label,s,i)}this.Dp.N().timeScale.allowBoldLabels&&(t.font=this.Em());for(const e of s)if(e.weight>=n){const s=e.needAlignCoordinate?this.Bm(t,e.coord,e.label):e.coord;t.fillText(e.label,s,i)}}))}Bm(t,i,s){const n=this.Np.Vi(t,s),e=n/2,r=Math.floor(i-e)+.5;return r<0?i+=Math.abs(0-r):r+n>this.Lp.width&&(i-=Math.abs(this.Lp.width-(r+n))),i}Vm(t,i){const s=this.Tm();for(const n of t)for(const t of n.cs())t.Tt().nt(i,s)}Im(){return this.Dp.N().timeScale.borderColor}H(){return this.Ps.textColor}F(){return this.Ps.fontSize}nv(){return x(this.F(),this.Ps.fontFamily)}Em(){return x(this.F(),this.Ps.fontFamily,"bold")}Tm(){null===this.M&&(this.M={S:1,L:NaN,A:NaN,V:NaN,Ji:NaN,C:5,P:NaN,k:"",Gi:new rt,Rm:0});const t=this.M,i=this.nv();if(t.k!==i){const s=this.F();t.P=s,t.k=i,t.A=3*s/12,t.V=3*s/12,t.Ji=9*s/12,t.L=0,t.Rm=4*s/12,t.Gi.In()}return this.M}pv(t){this.Yf.style.cursor=1===t?"ew-resize":"default"}Sm(){const t=this.Dp.Qt(),i=t.N();i.leftPriceScale.visible||null===this.fm||(this.gm.removeChild(this.fm.Xf()),this.fm.m(),this.fm=null),i.rightPriceScale.visible||null===this.pm||(this.Mm.removeChild(this.pm.Xf()),this.pm.m(),this.pm=null);const s={Vc:this.Dp.Qt().Vc()},n=()=>i.leftPriceScale.borderVisible&&t.Et().N().borderVisible,e=()=>t.Fc();i.leftPriceScale.visible&&null===this.fm&&(this.fm=new Is("left",i,s,n,e),this.gm.appendChild(this.fm.Xf())),i.rightPriceScale.visible&&null===this.pm&&(this.pm=new Is("right",i,s,n,e),this.Mm.appendChild(this.pm.Xf()))}}const Os=!!ns&&!!navigator.userAgentData&&navigator.userAgentData.brands.some((t=>t.brand.includes("Chromium")))&&!!ns&&(navigator?.userAgentData?.platform?"Windows"===navigator.userAgentData.platform:navigator.userAgent.toLowerCase().indexOf("win")>=0);class Ns{constructor(t,i,s){var n;this.Am=[],this.zm=[],this.Lm=0,this.sl=0,this.Mo=0,this.Om=0,this.Nm=0,this.Fm=null,this.Wm=!1,this.Sv=new d,this.xv=new d,this.Ku=new d,this.Hm=null,this.Um=null,this.Rp=t,this.Ps=i,this.o_=s,this.kp=document.createElement("div"),this.kp.classList.add("tv-lightweight-charts"),this.kp.style.overflow="hidden",this.kp.style.direction="ltr",this.kp.style.width="100%",this.kp.style.height="100%",(n=this.kp).style.userSelect="none",n.style.webkitUserSelect="none",n.style.msUserSelect="none",n.style.MozUserSelect="none",n.style.webkitTapHighlightColor="transparent",this.$m=document.createElement("table"),this.$m.setAttribute("cellspacing","0"),this.kp.appendChild(this.$m),this.qm=this.Ym.bind(this),Fs(this.Ps)&&this.jm(!0),this.ts=new Oi(this.Zu.bind(this),this.Ps,s),this.Qt().uc().i(this.Km.bind(this),this),this.Xm=new Ls(this,this.o_),this.$m.appendChild(this.Xm.Xf());const e=i.autoSize&&this.Zm();let r=this.Ps.width,h=this.Ps.height;if(e||0===r||0===h){const i=t.getBoundingClientRect();r=r||i.width,h=h||i.height}this.Gm(r,h),this.Jm(),t.appendChild(this.kp),this.Qm(),this.ts.Et().Iu().i(this.ts.Bh.bind(this.ts),this),this.ts.Vo().i(this.ts.Bh.bind(this.ts),this)}Qt(){return this.ts}N(){return this.Ps}Uf(){return this.Am}tw(){return this.Xm}m(){this.jm(!1),0!==this.Lm&&window.cancelAnimationFrame(this.Lm),this.ts.uc().u(this),this.ts.Et().Iu().u(this),this.ts.Vo().u(this),this.ts.m();for(const t of this.Am)this.$m.removeChild(t.Xf()),t.Kv().u(this),t.Xv().u(this),t.m();this.Am=[];for(const t of this.zm)this.iw(t);this.zm=[],u(this.Xm).m(),null!==this.kp.parentElement&&this.kp.parentElement.removeChild(this.kp),this.Ku.m(),this.Sv.m(),this.xv.m(),this.sw()}Gm(i,s,n=!1){if(this.sl===s&&this.Mo===i)return;const e=function(i){const s=Math.floor(i.width),n=Math.floor(i.height);return t({width:s-s%2,height:n-n%2})}(t({width:i,height:s}));this.sl=e.height,this.Mo=e.width;const r=this.sl+"px",h=this.Mo+"px";u(this.kp).style.height=r,u(this.kp).style.width=h,this.$m.style.height=r,this.$m.style.width=h,n?this.nw(G.gn(),performance.now()):this.ts.Bh()}av(t){void 0===t&&(t=G.gn());for(let i=0;i<this.Am.length;i++)this.Am[i].av(t.en(i).tn);this.Ps.timeScale.visible&&this.Xm.av(t.nn())}hr(t){const i=Fs(this.Ps);this.ts.hr(t);const s=Fs(this.Ps);s!==i&&this.jm(s),t.layout?.panes&&this.ew(),this.Qm(),this.rw(t)}Kv(){return this.Sv}Xv(){return this.xv}uc(){return this.Ku}hw(){null!==this.Fm&&(this.nw(this.Fm,performance.now()),this.Fm=null);const t=this.aw(null),i=document.createElement("canvas");i.width=t.width,i.height=t.height;const s=u(i.getContext("2d"));return this.aw(s),i}lw(t){if("left"===t&&!this.ow())return 0;if("right"===t&&!this._w())return 0;if(0===this.Am.length)return 0;return u("left"===t?this.Am[0].sm():this.Am[0].nm()).hv()}uw(){return this.Ps.autoSize&&null!==this.Hm}tp(){return this.kp}cw(t){this.Um=t,this.Um?this.tp().style.setProperty("cursor",t):this.tp().style.removeProperty("cursor")}dw(){return this.Um}fw(t){return _(this.Am[t]).Zf()}ew(){this.zm.forEach((t=>{t.kt()}))}rw(t){(void 0!==t.autoSize||!this.Hm||void 0===t.width&&void 0===t.height)&&(t.autoSize&&!this.Hm&&this.Zm(),!1===t.autoSize&&null!==this.Hm&&this.sw(),t.autoSize||void 0===t.width&&void 0===t.height||this.Gm(t.width||this.Mo,t.height||this.sl))}aw(i){let s=0,n=0;const e=this.Am[0],r=(t,s)=>{let n=0;for(let e=0;e<this.Am.length;e++){const r=this.Am[e],h=u("left"===t?r.sm():r.nm()),a=h.Gf();if(null!==i&&h.Jf(i,s,n),n+=a.height,e<this.Am.length-1){const t=this.zm[e],r=t.Gf();null!==i&&t.Jf(i,s,n),n+=r.height}}};if(this.ow()){r("left",0);s+=u(e.sm()).Gf().width}for(let t=0;t<this.Am.length;t++){const e=this.Am[t],r=e.Gf();if(null!==i&&e.Jf(i,s,n),n+=r.height,t<this.Am.length-1){const e=this.zm[t],r=e.Gf();null!==i&&e.Jf(i,s,n),n+=r.height}}if(s+=e.Gf().width,this._w()){r("right",s);s+=u(e.nm()).Gf().width}const h=(t,s,n)=>{u("left"===t?this.Xm.xm():this.Xm.Cm()).Jf(u(i),s,n)};if(this.Ps.timeScale.visible){const t=this.Xm.Gf();if(null!==i){let s=0;this.ow()&&(h("left",s,n),s=u(e.sm()).Gf().width),this.Xm.Jf(i,s,n),s+=t.width,this._w()&&h("right",s,n)}n+=t.height}return t({width:s,height:n})}pw(){let i=0,s=0,n=0;for(const t of this.Am)this.ow()&&(s=Math.max(s,u(t.sm()).sv(),this.Ps.leftPriceScale.minimumWidth)),this._w()&&(n=Math.max(n,u(t.nm()).sv(),this.Ps.rightPriceScale.minimumWidth)),i+=t.Io();s=hs(s),n=hs(n);const e=this.Mo,r=this.sl,h=Math.max(e-s-n,0),a=1*this.zm.length,l=this.Ps.timeScale.visible;let o=l?Math.max(this.Xm.ym(),this.Ps.timeScale.minimumHeight):0;var _;o=(_=o)+_%2;const c=a+o,d=r<c?0:r-c,f=d/i;let p=0;const v=window.devicePixelRatio||1;for(let i=0;i<this.Am.length;++i){const e=this.Am[i];e.zv(this.ts.$s()[i]);let r=0,a=0;a=i===this.Am.length-1?Math.ceil((d-p)*v)/v:Math.round(e.Io()*f*v)/v,r=Math.max(a,2),p+=r,e.rv(t({width:h,height:r})),this.ow()&&e.Gv(s,"left"),this._w()&&e.Gv(n,"right"),e.hp()&&this.ts.cc(e.hp(),r)}this.Xm.km(t({width:l?h:0,height:o}),l?s:0,l?n:0),this.ts.Eo(h),this.Om!==s&&(this.Om=s),this.Nm!==n&&(this.Nm=n)}jm(t){t?this.kp.addEventListener("wheel",this.qm,{passive:!1}):this.kp.removeEventListener("wheel",this.qm)}mw(t){switch(t.deltaMode){case t.DOM_DELTA_PAGE:return 120;case t.DOM_DELTA_LINE:return 32}return Os?1/window.devicePixelRatio:1}Ym(t){if(!(0!==t.deltaX&&this.Ps.handleScroll.mouseWheel||0!==t.deltaY&&this.Ps.handleScale.mouseWheel))return;const i=this.mw(t),s=i*t.deltaX/100,n=-i*t.deltaY/100;if(t.cancelable&&t.preventDefault(),0!==n&&this.Ps.handleScale.mouseWheel){const i=Math.sign(n)*Math.min(1,Math.abs(n)),s=t.clientX-this.kp.getBoundingClientRect().left;this.Qt().Mc(s,i)}0!==s&&this.Ps.handleScroll.mouseWheel&&this.Qt().bc(-80*s)}nw(t,i){const s=t.nn();3===s&&this.ww(),3!==s&&2!==s||(this.gw(t),this.Mw(t,i),this.Xm.kt(),this.Am.forEach((t=>{t.Ov()})),3===this.Fm?.nn()&&(this.Fm.vn(t),this.ww(),this.gw(this.Fm),this.Mw(this.Fm,i),t=this.Fm,this.Fm=null)),this.av(t)}Mw(t,i){for(const s of t.pn())this.mn(s,i)}gw(t){const i=this.ts.$s();for(let s=0;s<i.length;s++)t.en(s).sn&&i[s].Go()}mn(t,i){const s=this.ts.Et();switch(t.an){case 0:s.Eu();break;case 1:s.Au(t.Wt);break;case 2:s.dn(t.Wt);break;case 3:s.fn(t.Wt);break;case 4:s.bu();break;case 5:t.Wt.Tu(i)||s.fn(t.Wt.Ru(i))}}Zu(t){null!==this.Fm?this.Fm.vn(t):this.Fm=t,this.Wm||(this.Wm=!0,this.Lm=window.requestAnimationFrame((t=>{if(this.Wm=!1,this.Lm=0,null!==this.Fm){const i=this.Fm;this.Fm=null,this.nw(i,t);for(const s of i.pn())if(5===s.an&&!s.Wt.Tu(t)){this.Qt()._n(s.Wt);break}}})))}ww(){this.Jm()}iw(t){this.$m.removeChild(t.Xf()),t.m()}Jm(){const t=this.ts.$s(),i=t.length,s=this.Am.length;for(let t=i;t<s;t++){const t=_(this.Am.pop());this.$m.removeChild(t.Xf()),t.Kv().u(this),t.Xv().u(this),t.m();const i=this.zm.pop();void 0!==i&&this.iw(i)}for(let n=s;n<i;n++){const i=new Vs(this,t[n]);if(i.Kv().i(this.bw.bind(this,i),this),i.Xv().i(this.Sw.bind(this,i),this),this.Am.push(i),n>0){const t=new fs(this,n-1,n);this.zm.push(t),this.$m.insertBefore(t.Xf(),this.Xm.Xf())}this.$m.insertBefore(i.Xf(),this.Xm.Xf())}for(let s=0;s<i;s++){const i=t[s],n=this.Am[s];n.hp()!==i?n.zv(i):n.Av()}this.Qm(),this.pw()}xw(t,i,s,n){const e=new Map;if(null!==t){this.ts.js().forEach((i=>{const s=i.Xs().Fr(t);null!==s&&e.set(i,s)}))}let r;if(null!==t){const i=this.ts.Et().ss(t)?.originalTime;void 0!==i&&(r=i)}const h=this.Qt().hc(),a=null!==h&&h.n_ instanceof jt?h.n_:void 0,l=null!==h&&void 0!==h.e_?h.e_.Kn:void 0,o=this.Cw(n);return{Pw:r,Re:t??void 0,kw:i??void 0,yw:-1!==o?o:void 0,Tw:a,Rw:e,Dw:l,Vw:s??void 0}}Cw(t){let i=-1;if(t)i=this.Am.indexOf(t);else{const t=this.Qt()._c().Us();null!==t&&(i=this.Qt().$s().indexOf(t))}return i}bw(t,i,s,n){this.Sv.p((()=>this.xw(i,s,n,t)))}Sw(t,i,s,n){this.xv.p((()=>this.xw(i,s,n,t)))}Km(t,i,s){this.cw(this.Qt().hc()?.h_??null),this.Ku.p((()=>this.xw(t,i,s)))}Qm(){const t=this.Ps.timeScale.visible?"":"none";this.Xm.Xf().style.display=t}ow(){return this.Am[0].hp().Wo().N().visible}_w(){return this.Am[0].hp().Ho().N().visible}Zm(){return"ResizeObserver"in window&&(this.Hm=new ResizeObserver((t=>{const i=t[t.length-1];i&&this.Gm(i.contentRect.width,i.contentRect.height)})),this.Hm.observe(this.Rp,{box:"border-box"}),!0)}sw(){null!==this.Hm&&this.Hm.disconnect(),this.Hm=null}}function Fs(t){return Boolean(t.handleScroll.mouseWheel||t.handleScale.mouseWheel)}function Ws(t){return void 0===t.open&&void 0===t.value}function Hs(t){return function(t){return void 0!==t.open}(t)||function(t){return void 0!==t.value}(t)}function Us(t,i,s,n){const e=s.value,r={Re:i,wt:t,Wt:[e,e,e,e],Pw:n};return void 0!==s.color&&(r.R=s.color),r}function $s(t,i,s,n){const e=s.value,r={Re:i,wt:t,Wt:[e,e,e,e],Pw:n};return void 0!==s.lineColor&&(r.vt=s.lineColor),void 0!==s.topColor&&(r.mr=s.topColor),void 0!==s.bottomColor&&(r.wr=s.bottomColor),r}function qs(t,i,s,n){const e=s.value,r={Re:i,wt:t,Wt:[e,e,e,e],Pw:n};return void 0!==s.topLineColor&&(r.gr=s.topLineColor),void 0!==s.bottomLineColor&&(r.Mr=s.bottomLineColor),void 0!==s.topFillColor1&&(r.br=s.topFillColor1),void 0!==s.topFillColor2&&(r.Sr=s.topFillColor2),void 0!==s.bottomFillColor1&&(r.Cr=s.bottomFillColor1),void 0!==s.bottomFillColor2&&(r.Pr=s.bottomFillColor2),r}function Ys(t,i,s,n){const e={Re:i,wt:t,Wt:[s.open,s.high,s.low,s.close],Pw:n};return void 0!==s.color&&(e.R=s.color),e}function js(t,i,s,n){const e={Re:i,wt:t,Wt:[s.open,s.high,s.low,s.close],Pw:n};return void 0!==s.color&&(e.R=s.color),void 0!==s.borderColor&&(e.Ht=s.borderColor),void 0!==s.wickColor&&(e.vr=s.wickColor),e}function Ks(t,i,s,n,e){const r=_(e)(s),h=Math.max(...r),a=Math.min(...r),l=r[r.length-1],o=[l,h,a,l],{time:u,color:c,...d}=s;return{Re:i,wt:t,Wt:o,Pw:n,se:d,R:c}}function Xs(t){return void 0!==t.Wt}function Zs(t,i){return void 0!==i.customValues&&(t.Iw=i.customValues),t}function Gs(t){return(i,s,n,e,r,h)=>function(t,i){return i?i(t):Ws(t)}(n,h)?Zs({wt:i,Re:s,Pw:e},n):Zs(t(i,s,n,e,r),n)}function Js(t){return{Candlestick:Gs(js),Bar:Gs(Ys),Area:Gs($s),Baseline:Gs(qs),Histogram:Gs(Us),Line:Gs(Us),Custom:Gs(Ks)}[t]}function Qs(t){return{Re:0,Bw:new Map,Hh:t}}function tn(t,i){if(void 0!==t&&0!==t.length)return{Ew:i.key(t[0].wt),Aw:i.key(t[t.length-1].wt)}}function sn(t){let i;return t.forEach((t=>{void 0===i&&(i=t.Pw)})),_(i)}class nn{constructor(t){this.zw=new Map,this.Lw=new Map,this.Ow=new Map,this.Nw=[],this.o_=t}m(){this.zw.clear(),this.Lw.clear(),this.Ow.clear(),this.Nw=[]}Fw(t,i){let s=0!==this.zw.size,n=!1;const e=this.Lw.get(t);if(void 0!==e)if(1===this.Lw.size)s=!1,n=!0,this.zw.clear();else for(const i of this.Nw)i.pointData.Bw.delete(t)&&(n=!0);let r=[];if(0!==i.length){const s=i.map((t=>t.time)),e=this.o_.createConverterToInternalObj(i),h=Js(t.Rr()),a=t.da(),l=t.pa();r=i.map(((i,r)=>{const o=e(i.time),_=this.o_.key(o);let u=this.zw.get(_);void 0===u&&(u=Qs(o),this.zw.set(_,u),n=!0);const c=h(o,u.Re,i,s[r],a,l);return u.Bw.set(t,c),c}))}s&&this.Ww(),this.Hw(t,r);let h=-1;if(n){const t=[];this.zw.forEach((i=>{t.push({timeWeight:0,time:i.Hh,pointData:i,originalTime:sn(i.Bw)})})),t.sort(((t,i)=>this.o_.key(t.time)-this.o_.key(i.time))),h=this.Uw(t)}return this.$w(t,h,function(t,i,s){const n=tn(t,s),e=tn(i,s);if(void 0!==n&&void 0!==e)return{qw:!1,zh:n.Aw>=e.Aw&&n.Ew>=e.Ew}}(this.Lw.get(t),e,this.o_))}Ec(t){return this.Fw(t,[])}Yw(t,i,s){const n=i;!function(t){void 0===t.Pw&&(t.Pw=t.time)}(n),this.o_.preprocessData(i);const e=this.o_.createConverterToInternalObj([i])(i.time),r=this.Ow.get(t);if(!s&&void 0!==r&&this.o_.key(e)<this.o_.key(r))throw new Error(`Cannot update oldest data, last time=${r}, new time=${e}`);let h=this.zw.get(this.o_.key(e));if(s&&void 0===h)throw new Error("Cannot update non-existing data point when historicalUpdate is true");const a=void 0===h;void 0===h&&(h=Qs(e),this.zw.set(this.o_.key(e),h));const l=Js(t.Rr()),o=t.da(),_=t.pa(),u=l(e,h.Re,i,n.Pw,o,_);h.Bw.set(t,u),s?this.jw(t,u,h.Re):this.Kw(t,u);const c={zh:Xs(u),qw:s};if(!a)return this.$w(t,-1,c);const d={timeWeight:0,time:h.Hh,pointData:h,originalTime:sn(h.Bw)},f=kt(this.Nw,this.o_.key(d.time),((t,i)=>this.o_.key(t.time)<i));this.Nw.splice(f,0,d);for(let t=f;t<this.Nw.length;++t)en(this.Nw[t].pointData,t);return this.o_.fillWeightsForPoints(this.Nw,f),this.$w(t,f,c)}Kw(t,i){let s=this.Lw.get(t);void 0===s&&(s=[],this.Lw.set(t,s));const n=0!==s.length?s[s.length-1]:null;null===n||this.o_.key(i.wt)>this.o_.key(n.wt)?Xs(i)&&s.push(i):Xs(i)?s[s.length-1]=i:s.splice(-1,1),this.Ow.set(t,i.wt)}jw(t,i,s){const n=this.Lw.get(t);if(void 0===n)return;const e=kt(n,s,((t,i)=>t.Re<i));Xs(i)?n[e]=i:n.splice(e,1)}Hw(t,i){0!==i.length?(this.Lw.set(t,i.filter(Xs)),this.Ow.set(t,i[i.length-1].wt)):(this.Lw.delete(t),this.Ow.delete(t))}Ww(){for(const t of this.Nw)0===t.pointData.Bw.size&&this.zw.delete(this.o_.key(t.time))}Uw(t){let i=-1;for(let s=0;s<this.Nw.length&&s<t.length;++s){const n=this.Nw[s],e=t[s];if(this.o_.key(n.time)!==this.o_.key(e.time)){i=s;break}e.timeWeight=n.timeWeight,en(e.pointData,s)}if(-1===i&&this.Nw.length!==t.length&&(i=Math.min(this.Nw.length,t.length)),-1===i)return-1;for(let s=i;s<t.length;++s)en(t[s].pointData,s);return this.o_.fillWeightsForPoints(t,i),this.Nw=t,i}Xw(){if(0===this.Lw.size)return null;let t=0;return this.Lw.forEach((i=>{0!==i.length&&(t=Math.max(t,i[i.length-1].Re))})),t}$w(t,i,s){const n={Oo:new Map,Et:{ou:this.Xw()}};if(-1!==i)this.Lw.forEach(((i,e)=>{n.Oo.set(e,{se:i,Zw:e===t?s:void 0})})),this.Lw.has(t)||n.Oo.set(t,{se:[],Zw:s}),n.Et.Gw=this.Nw,n.Et.Jw=i;else{const i=this.Lw.get(t);n.Oo.set(t,{se:i||[],Zw:s})}return n}}function en(t,i){t.Re=i,t.Bw.forEach((t=>{t.Re=i}))}function rn(t,i){return t.wt<i}function hn(t,i){return i<t.wt}function an(t,i,s){const n=i.Uh(),e=i.bi(),r=kt(t,n,rn),h=yt(t,e,hn);if(!s)return{from:r,to:h};let a=r,l=h;return r>0&&r<t.length&&t[r].wt>=n&&(a=r-1),h>0&&h<t.length&&t[h-1].wt<=e&&(l=h+1),{from:a,to:l}}class ln{constructor(t,i,s){this.Qw=!0,this.tg=!0,this.ig=!0,this.sg=[],this.ng=null,this.Jn=t,this.Qn=i,this.eg=s}kt(t){this.Qw=!0,"data"===t&&(this.tg=!0),"options"===t&&(this.ig=!0)}Tt(){return this.Jn.Vt()?(this.rg(),null===this.ng?null:this.hg):null}ag(){this.sg=this.sg.map((t=>({...t,...this.Jn.Rh().Dr(t.wt)})))}lg(){this.ng=null}rg(){this.tg&&(this.og(),this.tg=!1),this.ig&&(this.ag(),this.ig=!1),this.Qw&&(this._g(),this.Qw=!1)}_g(){const t=this.Jn.Ft(),i=this.Qn.Et();if(this.lg(),i.Ki()||t.Ki())return;const s=i.Pe();if(null===s)return;if(0===this.Jn.Xs().zr())return;const n=this.Jn.zt();null!==n&&(this.ng=an(this.sg,s,this.eg),this.ug(t,i,n.Wt),this.cg())}}class on{constructor(t,i){this.dg=t,this.Yi=i}nt(t,i,s){this.dg.draw(t,this.Yi,i,s)}}class _n extends ln{constructor(t,i,s){super(t,i,!1),this.sh=s,this.hg=new on(this.sh.renderer(),(i=>{const s=t.zt();return null===s?null:t.Ft().Nt(i,s.Wt)}))}fa(t){return this.sh.priceValueBuilder(t)}va(t){return this.sh.isWhitespace(t)}og(){const t=this.Jn.Rh();this.sg=this.Jn.Xs().Hr().map((i=>({wt:i.Re,_t:NaN,...t.Dr(i.Re),fg:i.se})))}ug(t,i){i._u(this.sg,b(this.ng))}cg(){this.sh.update({bars:this.sg.map(un),barSpacing:this.Qn.Et().vu(),visibleRange:this.ng},this.Jn.N())}}function un(t){return{x:t._t,time:t.wt,originalData:t.fg,barColor:t.cr}}const cn={color:"#2196f3"},dn=(t,i,s)=>{const n=c(s);return new _n(t,i,n)};function fn(t){const i={value:t.Wt[3],time:t.Pw};return void 0!==t.Iw&&(i.customValues=t.Iw),i}function pn(t){const i=fn(t);return void 0!==t.R&&(i.color=t.R),i}function vn(t){const i=fn(t);return void 0!==t.vt&&(i.lineColor=t.vt),void 0!==t.mr&&(i.topColor=t.mr),void 0!==t.wr&&(i.bottomColor=t.wr),i}function mn(t){const i=fn(t);return void 0!==t.gr&&(i.topLineColor=t.gr),void 0!==t.Mr&&(i.bottomLineColor=t.Mr),void 0!==t.br&&(i.topFillColor1=t.br),void 0!==t.Sr&&(i.topFillColor2=t.Sr),void 0!==t.Cr&&(i.bottomFillColor1=t.Cr),void 0!==t.Pr&&(i.bottomFillColor2=t.Pr),i}function wn(t){const i={open:t.Wt[0],high:t.Wt[1],low:t.Wt[2],close:t.Wt[3],time:t.Pw};return void 0!==t.Iw&&(i.customValues=t.Iw),i}function gn(t){const i=wn(t);return void 0!==t.R&&(i.color=t.R),i}function Mn(t){const i=wn(t),{R:s,Ht:n,vr:e}=t;return void 0!==s&&(i.color=s),void 0!==n&&(i.borderColor=n),void 0!==e&&(i.wickColor=e),i}function bn(t){return{Area:vn,Line:pn,Baseline:mn,Histogram:pn,Bar:gn,Candlestick:Mn,Custom:Sn}[t]}function Sn(t){const i=t.Pw;return{...t.se,time:i}}const xn={vertLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},horzLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},mode:1},Cn={vertLines:{color:"#D6DCDE",style:0,visible:!0},horzLines:{color:"#D6DCDE",style:0,visible:!0}},Pn={background:{type:"solid",color:"#FFFFFF"},textColor:"#191919",fontSize:12,fontFamily:S,panes:{enableResize:!0,separatorColor:"#E0E3EB",separatorHoverColor:"rgba(178, 181, 189, 0.2)"},attributionLogo:!0,colorSpace:"srgb",colorParsers:[]},kn={autoScale:!0,mode:0,invertScale:!1,alignLabels:!0,borderVisible:!0,borderColor:"#2B2B43",entireTextOnly:!1,visible:!1,ticksVisible:!1,scaleMargins:{bottom:.1,top:.2},minimumWidth:0,ensureEdgeTickMarksVisible:!1},yn={rightOffset:0,barSpacing:6,minBarSpacing:.5,maxBarSpacing:0,fixLeftEdge:!1,fixRightEdge:!1,lockVisibleTimeRangeOnResize:!1,rightBarStaysOnScroll:!1,borderVisible:!0,borderColor:"#2B2B43",visible:!0,timeVisible:!1,secondsVisible:!0,shiftVisibleRangeOnNewBar:!0,allowShiftVisibleRangeOnWhitespaceReplacement:!1,ticksVisible:!1,uniformDistribution:!1,minimumHeight:0,allowBoldLabels:!0,ignoreWhitespaceIndices:!1};function Tn(){return{addDefaultPane:!0,width:0,height:0,autoSize:!1,layout:Pn,crosshair:xn,grid:Cn,overlayPriceScales:{...kn},leftPriceScale:{...kn,visible:!1},rightPriceScale:{...kn,visible:!0},timeScale:yn,localization:{locale:ns?navigator.language:"",dateFormat:"dd MMM 'yy"},handleScroll:{mouseWheel:!0,pressedMouseMove:!0,horzTouchDrag:!0,vertTouchDrag:!0},handleScale:{axisPressedMouseMove:{time:!0,price:!0},axisDoubleClickReset:{time:!0,price:!0},mouseWheel:!0,pinch:!0},kineticScroll:{mouse:!1,touch:!0},trackingMode:{exitMode:1}}}class Rn{constructor(t,i,s){this.Wf=t,this.pg=i,this.vg=s??0}applyOptions(t){this.Wf.Qt().lc(this.pg,t,this.vg)}options(){return this.Yi().N()}width(){return Z(this.pg)?this.Wf.lw(this.pg):0}setVisibleRange(t){this.setAutoScale(!1),this.Yi().Wl(new mt(t.from,t.to))}getVisibleRange(){const t=this.Yi().Qe();return null===t?null:{from:t.$e(),to:t.qe()}}setAutoScale(t){this.applyOptions({autoScale:t})}Yi(){return u(this.Wf.Qt().oc(this.pg,this.vg)).Ft}}class Dn{constructor(t,i,s,n){this.Wf=t,this.Pt=s,this.mg=i,this.wg=n}getHeight(){return this.Pt.$t()}setHeight(t){const i=this.Wf.Qt(),s=i.Hc(this.Pt);i.fc(s,t)}getStretchFactor(){return this.Pt.Io()}setStretchFactor(t){this.Pt.Bo(t),this.Wf.Qt().Bh()}paneIndex(){return this.Wf.Qt().Hc(this.Pt)}moveTo(t){const i=this.paneIndex();i!==t&&(o(t>=0&&t<this.Wf.Uf().length,"Invalid pane index"),this.Wf.Qt().mc(i,t))}getSeries(){return this.Pt.Oo().map((t=>this.mg(t)))??[]}getHTMLElement(){const t=this.Wf.Uf();return t&&0!==t.length&&t[this.paneIndex()]?t[this.paneIndex()].Xf():null}attachPrimitive(t){this.Pt.ua(t),t.attached&&t.attached({chart:this.wg,requestUpdate:()=>this.Pt.Qt().Bh()})}detachPrimitive(t){this.Pt.ca(t)}priceScale(t){if(null===this.Pt.Do(t))throw new Error(`Cannot find price scale with id: ${t}`);return new Rn(this.Wf,t,this.paneIndex())}setPreserveEmptyPane(t){this.Pt.zo(t)}preserveEmptyPane(){return this.Pt.Lo()}addCustomSeries(t,i={},s=0){return this.wg.addCustomSeries(t,i,s)}addSeries(t,i={}){return this.wg.addSeries(t,i,this.paneIndex())}}const Vn={color:"#FF0000",price:0,lineStyle:2,lineWidth:1,lineVisible:!0,axisLabelVisible:!0,title:"",axisLabelColor:"",axisLabelTextColor:""};class In{constructor(t){this.ir=t}applyOptions(t){this.ir.hr(t)}options(){return this.ir.N()}gg(){return this.ir}}class Bn{constructor(t,i,s,n,e,r){this.Mg=new d,this.Jn=t,this.bg=i,this.Sg=s,this.o_=e,this.wg=n,this.xg=r}m(){this.Mg.m()}priceFormatter(){return this.Jn.ra()}priceToCoordinate(t){const i=this.Jn.zt();return null===i?null:this.Jn.Ft().Nt(t,i.Wt)}coordinateToPrice(t){const i=this.Jn.zt();return null===i?null:this.Jn.Ft().Ts(t,i.Wt)}barsInLogicalRange(t){if(null===t)return null;const i=new Di(new yi(t.from,t.to)).k_(),s=this.Jn.Xs();if(s.Ki())return null;const n=s.Fr(i.Uh(),1),e=s.Fr(i.bi(),-1),r=u(s.Lr()),h=u(s.Ks());if(null!==n&&null!==e&&n.Re>e.Re)return{barsBefore:t.from-r,barsAfter:h-t.to};const a={barsBefore:null===n||n.Re===r?t.from-r:n.Re-r,barsAfter:null===e||e.Re===h?h-t.to:h-e.Re};return null!==n&&null!==e&&(a.from=n.Pw,a.to=e.Pw),a}setData(t){this.o_,this.Jn.Rr(),this.bg.Cg(this.Jn,t),this.Pg("full")}update(t,i=!1){this.Jn.Rr(),this.bg.kg(this.Jn,t,i),this.Pg("update")}dataByIndex(t,i){const s=this.Jn.Xs().Fr(t,i);if(null===s)return null;return bn(this.seriesType())(s)}data(){const t=bn(this.seriesType());return this.Jn.Xs().Hr().map((i=>t(i)))}subscribeDataChanged(t){this.Mg.i(t)}unsubscribeDataChanged(t){this.Mg._(t)}applyOptions(t){this.Jn.hr(t)}options(){return g(this.Jn.N())}priceScale(){return this.Sg.priceScale(this.Jn.Ft().wa(),this.getPane().paneIndex())}createPriceLine(t){const i=f(g(Vn),t),s=this.Jn.Oh(i);return new In(s)}removePriceLine(t){this.Jn.Nh(t.gg())}priceLines(){return this.Jn.Fh().map((t=>new In(t)))}seriesType(){return this.Jn.Rr()}attachPrimitive(t){this.Jn.ua(t),t.attached&&t.attached({chart:this.wg,series:this,requestUpdate:()=>this.Jn.Qt().Bh(),horzScaleBehavior:this.o_})}detachPrimitive(t){this.Jn.ca(t),t.detached&&t.detached(),this.Jn.Qt().Bh()}getPane(){const t=this.Jn,i=u(this.Jn.Qt().Hn(t));return this.xg(i)}moveToPane(t){this.Jn.Qt().Oc(this.Jn,t)}seriesOrder(){const t=this.Jn.Qt().Hn(this.Jn);return null===t?-1:t.Oo().indexOf(this.Jn)}setSeriesOrder(t){const i=this.Jn.Qt().Hn(this.Jn);null!==i&&i.Qo(this.Jn,t)}Pg(t){this.Mg.v()&&this.Mg.p(t)}}class En{constructor(t,i,s){this.yg=new d,this.z_=new d,this.wm=new d,this.ts=t,this.uh=t.Et(),this.Xm=i,this.uh.Du().i(this.Tg.bind(this)),this.uh.Vu().i(this.Rg.bind(this)),this.Xm.Pm().i(this.Dg.bind(this)),this.o_=s}m(){this.uh.Du().u(this),this.uh.Vu().u(this),this.Xm.Pm().u(this),this.yg.m(),this.z_.m(),this.wm.m()}scrollPosition(){return this.uh.wu()}scrollToPosition(t,i){i?this.uh.yu(t,1e3):this.ts.fn(t)}scrollToRealTime(){this.uh.ku()}getVisibleRange(){const t=this.uh.su();return null===t?null:{from:t.from.originalTime,to:t.to.originalTime}}setVisibleRange(t){const i={from:this.o_.convertHorzItemToInternal(t.from),to:this.o_.convertHorzItemToInternal(t.to)},s=this.uh.hu(i);this.ts.zc(s)}getVisibleLogicalRange(){const t=this.uh.iu();return null===t?null:{from:t.Uh(),to:t.bi()}}setVisibleLogicalRange(t){o(t.from<=t.to,"The from index cannot be after the to index."),this.ts.zc(t)}resetTimeScale(){this.ts.cn()}fitContent(){this.ts.Eu()}logicalToCoordinate(t){const i=this.ts.Et();return i.Ki()?null:i.qt(t)}coordinateToLogical(t){return this.uh.Ki()?null:this.uh.uu(t)}timeToIndex(t,i){const s=this.o_.convertHorzItemToInternal(t);return this.uh.J_(s,i)}timeToCoordinate(t){const i=this.timeToIndex(t,!1);return null===i?null:this.uh.qt(i)}coordinateToTime(t){const i=this.ts.Et(),s=i.uu(t),n=i.ss(s);return null===n?null:n.originalTime}width(){return this.Xm.Zf().width}height(){return this.Xm.Zf().height}subscribeVisibleTimeRangeChange(t){this.yg.i(t)}unsubscribeVisibleTimeRangeChange(t){this.yg._(t)}subscribeVisibleLogicalRangeChange(t){this.z_.i(t)}unsubscribeVisibleLogicalRangeChange(t){this.z_._(t)}subscribeSizeChange(t){this.wm.i(t)}unsubscribeSizeChange(t){this.wm._(t)}applyOptions(t){this.uh.hr(t)}options(){return{...g(this.uh.N()),barSpacing:this.uh.vu()}}Tg(){this.yg.v()&&this.yg.p(this.getVisibleRange())}Rg(){this.z_.v()&&this.z_.p(this.getVisibleLogicalRange())}Dg(t){this.wm.p(t.width,t.height)}}function An(t){if(void 0===t||"custom"===t.type)return;const i=t;void 0!==i.minMove&&void 0===i.precision&&(i.precision=function(t){if(t>=1)return 0;let i=0;for(;i<8;i++){const s=Math.round(t);if(Math.abs(s-t)<1e-8)return i;t*=10}return i}(i.minMove))}function zn(t){return function(t){if(w(t.handleScale)){const i=t.handleScale;t.handleScale={axisDoubleClickReset:{time:i,price:i},axisPressedMouseMove:{time:i,price:i},mouseWheel:i,pinch:i}}else if(void 0!==t.handleScale){const{axisPressedMouseMove:i,axisDoubleClickReset:s}=t.handleScale;w(i)&&(t.handleScale.axisPressedMouseMove={time:i,price:i}),w(s)&&(t.handleScale.axisDoubleClickReset={time:s,price:s})}const i=t.handleScroll;w(i)&&(t.handleScroll={horzTouchDrag:i,vertTouchDrag:i,mouseWheel:i,pressedMouseMove:i})}(t),t}class Ln{constructor(t,i,s){this.Vg=new Map,this.Ig=new Map,this.Bg=new d,this.Eg=new d,this.Ag=new d,this.$u=new WeakMap,this.zg=new nn(i);const n=void 0===s?g(Tn()):f(g(Tn()),zn(s));this.Lg=i,this.Wf=new Ns(t,n,i),this.Wf.Kv().i((t=>{this.Bg.v()&&this.Bg.p(this.Og(t()))}),this),this.Wf.Xv().i((t=>{this.Eg.v()&&this.Eg.p(this.Og(t()))}),this),this.Wf.uc().i((t=>{this.Ag.v()&&this.Ag.p(this.Og(t()))}),this);const e=this.Wf.Qt();this.Ng=new En(e,this.Wf.tw(),this.Lg)}remove(){this.Wf.Kv().u(this),this.Wf.Xv().u(this),this.Wf.uc().u(this),this.Ng.m(),this.Wf.m(),this.Vg.clear(),this.Ig.clear(),this.Bg.m(),this.Eg.m(),this.Ag.m(),this.zg.m()}resize(t,i,s){this.autoSizeActive()||this.Wf.Gm(t,i,s)}addCustomSeries(t,i={},s=0){const n=(t=>({type:"Custom",isBuiltIn:!1,defaultOptions:{...cn,...t.defaultOptions()},Fg:dn,Wg:t}))(c(t));return this.Hg(n,i,s)}addSeries(t,i={},s=0){return this.Hg(t,i,s)}removeSeries(t){const i=_(this.Vg.get(t)),s=this.zg.Ec(i);this.Wf.Qt().Ec(i),this.Ug(s),this.Vg.delete(t),this.Ig.delete(i)}Cg(t,i){this.Ug(this.zg.Fw(t,i))}kg(t,i,s){this.Ug(this.zg.Yw(t,i,s))}subscribeClick(t){this.Bg.i(t)}unsubscribeClick(t){this.Bg._(t)}subscribeCrosshairMove(t){this.Ag.i(t)}unsubscribeCrosshairMove(t){this.Ag._(t)}subscribeDblClick(t){this.Eg.i(t)}unsubscribeDblClick(t){this.Eg._(t)}priceScale(t,i=0){return new Rn(this.Wf,t,i)}timeScale(){return this.Ng}applyOptions(t){this.Wf.hr(zn(t))}options(){return this.Wf.N()}takeScreenshot(){return this.Wf.hw()}addPane(t=!1){const i=this.Wf.Qt().Uc();return i.zo(t),this.$g(i)}removePane(t){this.Wf.Qt().dc(t)}swapPanes(t,i){this.Wf.Qt().vc(t,i)}autoSizeActive(){return this.Wf.uw()}chartElement(){return this.Wf.tp()}panes(){return this.Wf.Qt().$s().map((t=>this.$g(t)))}paneSize(t=0){const i=this.Wf.fw(t);return{height:i.height,width:i.width}}setCrosshairPosition(t,i,s){const n=this.Vg.get(s);if(void 0===n)return;const e=this.Wf.Qt().Hn(n);null!==e&&this.Wf.Qt().Tc(t,i,e)}clearCrosshairPosition(){this.Wf.Qt().Rc(!0)}horzBehaviour(){return this.Lg}Hg(t,i={},s=0){o(void 0!==t.Fg),An(i.priceFormat),"Candlestick"===t.type&&function(t){void 0!==t.borderColor&&(t.borderUpColor=t.borderColor,t.borderDownColor=t.borderColor),void 0!==t.wickColor&&(t.wickUpColor=t.wickColor,t.wickDownColor=t.wickColor)}(i);const n=f(g(e),g(t.defaultOptions),i),r=t.Fg,h=new jt(this.Wf.Qt(),t.type,n,r,t.Wg);this.Wf.Qt().Ic(h,s);const a=new Bn(h,this,this,this,this.Lg,(t=>this.$g(t)));return this.Vg.set(a,h),this.Ig.set(h,a),a}Ug(t){const i=this.Wf.Qt();i.Dc(t.Et.ou,t.Et.Gw,t.Et.Jw),t.Oo.forEach(((t,i)=>i.ht(t.se,t.Zw))),i.Et().j_(),i.pu()}qg(t){return _(this.Ig.get(t))}Og(t){const i=new Map;t.Rw.forEach(((t,s)=>{const n=s.Rr(),e=bn(n)(t);if("Custom"!==n)o(Hs(e));else{const t=s.pa();o(!t||!1===t(e))}i.set(this.qg(s),e)}));const s=void 0!==t.Tw&&this.Ig.has(t.Tw)?this.qg(t.Tw):void 0;return{time:t.Pw,logical:t.Re,point:t.kw,paneIndex:t.yw,hoveredSeries:s,hoveredObjectId:t.Dw,seriesData:i,sourceEvent:t.Vw}}$g(t){let i=this.$u.get(t);return i||(i=new Dn(this.Wf,(t=>this.qg(t)),t,this),this.$u.set(t,i)),i}}function On(t){if(m(t)){const i=document.getElementById(t);return o(null!==i,`Cannot find element in DOM with id=${t}`),i}return t}function Nn(t,i,s){const n=On(t),e=new Ln(n,i,s);return i.setOptions(e.options()),e}function Fn(t,i){return Nn(t,new ss,ss.ad(i))}function Wn(){return ss}class Hn extends ln{constructor(t,i){super(t,i,!0)}ug(t,i,s){i._u(this.sg,b(this.ng)),t.$l(this.sg,s,b(this.ng))}Yg(t,i){return{wt:t,gt:i,_t:NaN,ut:NaN}}og(){const t=this.Jn.Rh();this.sg=this.Jn.Xs().Hr().map((i=>{const s=i.Wt[3];return this.jg(i.Re,s,t)}))}}function Un(t,i,s,n,e,r,h){if(0===i.length||n.from>=i.length||n.to<=0)return;const{context:a,horizontalPixelRatio:l,verticalPixelRatio:o}=t,_=i[n.from];let u=r(t,_),c=_;if(n.to-n.from<2){const i=e/2;a.beginPath();const s={_t:_._t-i,ut:_.ut},n={_t:_._t+i,ut:_.ut};a.moveTo(s._t*l,s.ut*o),a.lineTo(n._t*l,n.ut*o),h(t,u,s,n)}else{const e=(i,s)=>{h(t,u,c,s),a.beginPath(),u=i,c=s};let d=c;a.beginPath(),a.moveTo(_._t*l,_.ut*o);for(let h=n.from+1;h<n.to;++h){d=i[h];const n=r(t,d);switch(s){case 0:a.lineTo(d._t*l,d.ut*o);break;case 1:a.lineTo(d._t*l,i[h-1].ut*o),n!==u&&(e(n,d),a.lineTo(d._t*l,i[h-1].ut*o)),a.lineTo(d._t*l,d.ut*o);break;case 2:{const[t,s]=jn(i,h-1,h);a.bezierCurveTo(t._t*l,t.ut*o,s._t*l,s.ut*o,d._t*l,d.ut*o);break}}1!==s&&n!==u&&(e(n,d),a.moveTo(d._t*l,d.ut*o))}(c!==d||c===d&&1===s)&&h(t,u,c,d)}}const $n=6;function qn(t,i){return{_t:t._t-i._t,ut:t.ut-i.ut}}function Yn(t,i){return{_t:t._t/i,ut:t.ut/i}}function jn(t,i,s){const n=Math.max(0,i-1),e=Math.min(t.length-1,s+1);var r,h;return[(r=t[i],h=Yn(qn(t[s],t[n]),$n),{_t:r._t+h._t,ut:r.ut+h.ut}),qn(t[s],Yn(qn(t[e],t[i]),$n))]}function Kn(t,i){const s=t.context;s.strokeStyle=i,s.stroke()}class Xn extends R{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et(t){if(null===this.rt)return;const{ot:i,lt:s,Kg:n,Xg:e,ct:r,Xt:h,Zg:l}=this.rt;if(null===s)return;const o=t.context;o.lineCap="butt",o.lineWidth=r*t.verticalPixelRatio,a(o,h),o.lineJoin="round";const _=this.Gg.bind(this);void 0!==e&&Un(t,i,e,s,n,_,Kn),l&&function(t,i,s,n,e){if(n.to-n.from<=0)return;const{horizontalPixelRatio:r,verticalPixelRatio:h,context:a}=t;let l=null;const o=Math.max(1,Math.floor(r))%2/2,_=s*h+o;for(let s=n.to-1;s>=n.from;--s){const n=i[s];if(n){const i=e(t,n);i!==l&&(a.beginPath(),null!==l&&a.fill(),a.fillStyle=i,l=i);const s=Math.round(n._t*r)+o,u=n.ut*h;a.moveTo(s,u),a.arc(s,u,_,0,2*Math.PI)}}a.fill()}(t,i,l,s,_)}}class Zn extends Xn{Gg(t,i){return i.vt}}class Gn extends Hn{constructor(){super(...arguments),this.hg=new Zn}jg(t,i,s){return{...this.Yg(t,i),...s.Dr(t)}}cg(){const t=this.Jn.N(),i={ot:this.sg,Xt:t.lineStyle,Xg:t.lineVisible?t.lineType:void 0,ct:t.lineWidth,Zg:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0,lt:this.ng,Kg:this.Qn.Et().vu()};this.hg.ht(i)}}const Jn={type:"Line",isBuiltIn:!0,defaultOptions:{color:"#2196f3",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Fg:(t,i)=>new Gn(t,i)};function Qn(t,i){return t.weight>i.weight?t:i}class te{constructor(){this.Jg=new d,this.Qg=function(t){let i=!1;return function(...s){i||(i=!0,queueMicrotask((()=>{t(...s),i=!1})))}}((()=>this.Jg.p(this.tM))),this.tM=0}iM(){return this.Jg}m(){this.Jg.m()}options(){return this.Ps}setOptions(t){this.Ps=t}preprocessData(t){}updateFormatter(t){this.Ps&&(this.Ps.localization=t)}createConverterToInternalObj(t){return this.Qg(),t=>(t>this.tM&&(this.tM=t),t)}key(t){return t}cacheKey(t){return t}convertHorzItemToInternal(t){return t}formatHorzItem(t){return this.sM(t)}formatTickmark(t){return this.sM(t.time)}maxTickMarkWeight(t){return t.reduce(Qn,t[0]).weight}fillWeightsForPoints(t,i){for(let n=i;n<t.length;++n)t[n].timeWeight=(s=t[n].time)%120==0?10:s%60==0?9:s%36==0?8:s%12==0?7:s%6==0?6:s%3==0?5:s%1==0?4:0;var s;this.tM=t[t.length-1].time,this.Qg()}sM(t){if(this.Ps.localization?.timeFormatter)return this.Ps.localization.timeFormatter(t);if(t<12)return`${t}M`;const i=Math.floor(t/12),s=t%12;return 0===s?`${i}Y`:`${i}Y${s}M`}}const ie={yieldCurve:{baseResolution:1,minimumTimeRange:120,startTimeRange:0},timeScale:{ignoreWhitespaceIndices:!0},leftPriceScale:{visible:!0},rightPriceScale:{visible:!1},localization:{priceFormatter:t=>t.toFixed(3)+"%"}},se={lastValueVisible:!1,priceLineVisible:!1};class ne extends Ln{constructor(t,i){const s=f(ie,i||{}),n=new te;super(t,n,s),n.setOptions(this.options()),this._initWhitespaceSeries()}addSeries(t,i={},s=0){if(t.isBuiltIn&&!1===["Area","Line"].includes(t.type))throw new Error("Yield curve only support Area and Line series");const n={...se,...i};return super.addSeries(t,n,s)}_initWhitespaceSeries(){const t=this.horzBehaviour(),i=this.addSeries(Jn);let s;function n(n){const e=function(t,i){return{le:Math.max(0,t.startTimeRange),oe:Math.max(0,t.minimumTimeRange,i||0),nM:Math.max(1,t.baseResolution)}}(t.options().yieldCurve,n),r=(({le:t,oe:i,nM:s})=>`${t}~${i}~${s}`)(e);r!==s&&(s=r,i.setData(function({le:t,oe:i,nM:s}){return Array.from({length:Math.floor((i-t)/s)+1},((i,n)=>({time:t+n*s})))}(e)))}n(0),t.iM().i(n)}}function ee(t,i){const s=On(t);return new ne(s,i)}function re(t,i){return t.weight>i.weight?t:i}class he{options(){return this.Ps}setOptions(t){this.Ps=t}preprocessData(t){}updateFormatter(t){this.Ps&&(this.Ps.localization=t)}createConverterToInternalObj(t){return t=>t}key(t){return t}cacheKey(t){return t}convertHorzItemToInternal(t){return t}formatHorzItem(t){return t.toFixed(this.Cn())}formatTickmark(t,i){return t.time.toFixed(this.Cn())}maxTickMarkWeight(t){return t.reduce(re,t[0]).weight}fillWeightsForPoints(t,i){for(let n=i;n<t.length;++n)t[n].timeWeight=(s=t[n].time)===100*Math.ceil(s/100)?8:s===50*Math.ceil(s/50)?7:s===25*Math.ceil(s/25)?6:s===10*Math.ceil(s/10)?5:s===5*Math.ceil(s/5)?4:s===Math.ceil(s)?3:2*s===Math.ceil(2*s)?1:0;var s}Cn(){return this.Ps.localization.precision}}function ae(t,i){return Nn(t,new he,i)}function le(t,i,s,n,e){const{context:r,horizontalPixelRatio:h,verticalPixelRatio:a}=i;r.lineTo(e._t*h,t*a),r.lineTo(n._t*h,t*a),r.closePath(),r.fillStyle=s,r.fill()}class oe extends R{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et(t){if(null===this.rt)return;const{ot:i,lt:s,Kg:n,ct:e,Xt:r,Xg:h}=this.rt,l=this.rt.eM??(this.rt.rM?0:t.mediaSize.height);if(null===s)return;const o=t.context;o.lineCap="butt",o.lineJoin="round",o.lineWidth=e,a(o,r),o.lineWidth=1,Un(t,i,h,s,n,this.hM.bind(this),le.bind(null,l))}}class _e{aM(t,i){const s=this.lM,{oM:n,_M:e,uM:r,cM:h,eM:a,dM:l,fM:o}=i;if(void 0===this.pM||void 0===s||s.oM!==n||s._M!==e||s.uM!==r||s.cM!==h||s.eM!==a||s.dM!==l||s.fM!==o){const{verticalPixelRatio:s}=t,_=a||l>0?s:1,u=l*_,c=o===t.bitmapSize.height?o:o*_,d=(a??0)*_,f=t.context.createLinearGradient(0,u,0,c);if(f.addColorStop(0,n),null!=a){const t=Gt((d-u)/(c-u),0,1);f.addColorStop(t,e),f.addColorStop(t,r)}f.addColorStop(1,h),this.pM=f,this.lM=i}return this.pM}}class ue extends oe{constructor(){super(...arguments),this.vM=new _e}hM(t,i){const s=this.rt;return this.vM.aM(t,{oM:i.br,_M:i.Sr,uM:i.Cr,cM:i.Pr,eM:s.eM,dM:s.dM??0,fM:s.fM??t.bitmapSize.height})}}class ce extends Xn{constructor(){super(...arguments),this.mM=new _e}Gg(t,i){const s=this.rt;return this.mM.aM(t,{oM:i.gr,_M:i.gr,uM:i.Mr,cM:i.Mr,eM:s.eM,dM:s.dM??0,fM:s.fM??t.bitmapSize.height})}}class de extends Hn{constructor(t,i){super(t,i),this.hg=new T,this.wM=new ue,this.gM=new ce,this.hg.st([this.wM,this.gM])}jg(t,i,s){return{...this.Yg(t,i),...s.Dr(t)}}cg(){const t=this.Jn.zt();if(null===t)return;const i=this.Jn.N(),s=this.Jn.Ft().Nt(i.baseValue.price,t.Wt),n=this.Qn.Et().vu();if(null===this.ng||0===this.sg.length)return;let e,r;if(i.relativeGradient){e=this.sg[this.ng.from].ut,r=this.sg[this.ng.from].ut;for(let t=this.ng.from;t<this.ng.to;t++){const i=this.sg[t];i.ut<e&&(e=i.ut),i.ut>r&&(r=i.ut)}}this.wM.ht({ot:this.sg,ct:i.lineWidth,Xt:i.lineStyle,Xg:i.lineType,eM:s,dM:e,fM:r,rM:!1,lt:this.ng,Kg:n}),this.gM.ht({ot:this.sg,ct:i.lineWidth,Xt:i.lineStyle,Xg:i.lineVisible?i.lineType:void 0,Zg:i.pointMarkersVisible?i.pointMarkersRadius||i.lineWidth/2+2:void 0,eM:s,dM:e,fM:r,lt:this.ng,Kg:n})}}const fe={type:"Baseline",isBuiltIn:!0,defaultOptions:{baseValue:{type:"price",price:0},relativeGradient:!1,topFillColor1:"rgba(38, 166, 154, 0.28)",topFillColor2:"rgba(38, 166, 154, 0.05)",topLineColor:"rgba(38, 166, 154, 1)",bottomFillColor1:"rgba(239, 83, 80, 0.05)",bottomFillColor2:"rgba(239, 83, 80, 0.28)",bottomLineColor:"rgba(239, 83, 80, 1)",lineWidth:3,lineStyle:0,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Fg:(t,i)=>new de(t,i)};class pe extends oe{constructor(){super(...arguments),this.vM=new _e}hM(t,i){return this.vM.aM(t,{oM:i.mr,_M:"",uM:"",cM:i.wr,dM:this.rt?.dM??0,fM:t.bitmapSize.height})}}class ve extends Hn{constructor(t,i){super(t,i),this.hg=new T,this.MM=new pe,this.bM=new Zn,this.hg.st([this.MM,this.bM])}jg(t,i,s){return{...this.Yg(t,i),...s.Dr(t)}}cg(){const t=this.Jn.N();if(null===this.ng||0===this.sg.length)return;let i;if(t.relativeGradient){i=this.sg[this.ng.from].ut;for(let t=this.ng.from;t<this.ng.to;t++){const s=this.sg[t];s.ut<i&&(i=s.ut)}}this.MM.ht({Xg:t.lineType,ot:this.sg,Xt:t.lineStyle,ct:t.lineWidth,eM:null,dM:i,rM:t.invertFilledArea,lt:this.ng,Kg:this.Qn.Et().vu()}),this.bM.ht({Xg:t.lineVisible?t.lineType:void 0,ot:this.sg,Xt:t.lineStyle,ct:t.lineWidth,lt:this.ng,Kg:this.Qn.Et().vu(),Zg:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0})}}const me={type:"Area",isBuiltIn:!0,defaultOptions:{topColor:"rgba( 46, 220, 135, 0.4)",bottomColor:"rgba( 40, 221, 100, 0)",invertFilledArea:!1,relativeGradient:!1,lineColor:"#33D778",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Fg:(t,i)=>new ve(t,i)};class we extends R{constructor(){super(...arguments),this.Yt=null,this.SM=0,this.xM=0}ht(t){this.Yt=t}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.Yt||0===this.Yt.Xs.length||null===this.Yt.lt)return;if(this.SM=this.CM(i),this.SM>=2){Math.max(1,Math.floor(i))%2!=this.SM%2&&this.SM--}this.xM=this.Yt.PM?Math.min(this.SM,Math.floor(i)):this.SM;let n=null;const e=this.xM<=this.SM&&this.Yt.vu>=Math.floor(1.5*i);for(let r=this.Yt.lt.from;r<this.Yt.lt.to;++r){const h=this.Yt.Xs[r];n!==h.cr&&(t.fillStyle=h.cr,n=h.cr);const a=Math.floor(.5*this.xM),l=Math.round(h._t*i),o=l-a,_=this.xM,u=o+_-1,c=Math.min(h.Kl,h.Xl),d=Math.max(h.Kl,h.Xl),f=Math.round(c*s)-a,p=Math.round(d*s)+a,v=Math.max(p-f,this.xM);t.fillRect(o,f,_,v);const m=Math.ceil(1.5*this.SM);if(e){if(this.Yt.kM){const i=l-m;let n=Math.max(f,Math.round(h.jl*s)-a),e=n+_-1;e>f+v-1&&(e=f+v-1,n=e-_+1),t.fillRect(i,n,o-i,e-n+1)}const i=l+m;let n=Math.max(f,Math.round(h.Zl*s)-a),e=n+_-1;e>f+v-1&&(e=f+v-1,n=e-_+1),t.fillRect(u+1,n,i-u,e-n+1)}}}CM(t){const i=Math.floor(t);return Math.max(i,Math.floor(function(t,i){return Math.floor(.3*t*i)}(u(this.Yt).vu,t)))}}class ge extends ln{constructor(t,i){super(t,i,!1)}ug(t,i,s){i._u(this.sg,b(this.ng)),t.Yl(this.sg,s,b(this.ng))}yM(t,i,s){return{wt:t,qh:i.Wt[0],Yh:i.Wt[1],jh:i.Wt[2],Kh:i.Wt[3],_t:NaN,jl:NaN,Kl:NaN,Xl:NaN,Zl:NaN}}og(){const t=this.Jn.Rh();this.sg=this.Jn.Xs().Hr().map((i=>this.jg(i.Re,i,t)))}}class Me extends ge{constructor(){super(...arguments),this.hg=new we}jg(t,i,s){return{...this.yM(t,i,s),...s.Dr(t)}}cg(){const t=this.Jn.N();this.hg.ht({Xs:this.sg,vu:this.Qn.Et().vu(),kM:t.openVisible,PM:t.thinBars,lt:this.ng})}}const be={type:"Bar",isBuiltIn:!0,defaultOptions:{upColor:"#26a69a",downColor:"#ef5350",openVisible:!0,thinBars:!0},Fg:(t,i)=>new Me(t,i)};class Se extends R{constructor(){super(...arguments),this.Yt=null,this.SM=0}ht(t){this.Yt=t}et(t){if(null===this.Yt||0===this.Yt.Xs.length||null===this.Yt.lt)return;const{horizontalPixelRatio:i}=t;if(this.SM=function(t,i){if(t>=2.5&&t<=4)return Math.floor(3*i);const s=1-.2*Math.atan(Math.max(4,t)-4)/(.5*Math.PI),n=Math.floor(t*s*i),e=Math.floor(t*i),r=Math.min(n,e);return Math.max(Math.floor(i),r)}(this.Yt.vu,i),this.SM>=2){Math.floor(i)%2!=this.SM%2&&this.SM--}const s=this.Yt.Xs;this.Yt.TM&&this.RM(t,s,this.Yt.lt),this.Yt.Mi&&this._v(t,s,this.Yt.lt);const n=this.DM(i);(!this.Yt.Mi||this.SM>2*n)&&this.VM(t,s,this.Yt.lt)}RM(t,i,s){if(null===this.Yt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="",a=Math.min(Math.floor(e),Math.floor(this.Yt.vu*e));a=Math.max(Math.floor(e),Math.min(a,this.SM));const l=Math.floor(.5*a);let o=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.pr!==h&&(n.fillStyle=s.pr,h=s.pr);const _=Math.round(Math.min(s.jl,s.Zl)*r),u=Math.round(Math.max(s.jl,s.Zl)*r),c=Math.round(s.Kl*r),d=Math.round(s.Xl*r);let f=Math.round(e*s._t)-l;const p=f+a-1;null!==o&&(f=Math.max(o+1,f),f=Math.min(f,p));const v=p-f+1;n.fillRect(f,c,v,_-c),n.fillRect(f,u+1,v,d-u),o=p}}DM(t){let i=Math.floor(1*t);this.SM<=2*i&&(i=Math.floor(.5*(this.SM-1)));const s=Math.max(Math.floor(t),i);return this.SM<=2*s?Math.max(Math.floor(t),Math.floor(1*t)):s}_v(t,i,s){if(null===this.Yt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="";const a=this.DM(e);let l=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.dr!==h&&(n.fillStyle=s.dr,h=s.dr);let o=Math.round(s._t*e)-Math.floor(.5*this.SM);const _=o+this.SM-1,u=Math.round(Math.min(s.jl,s.Zl)*r),c=Math.round(Math.max(s.jl,s.Zl)*r);if(null!==l&&(o=Math.max(l+1,o),o=Math.min(o,_)),this.Yt.vu*e>2*a)z(n,o,u,_-o+1,c-u+1,a);else{const t=_-o+1;n.fillRect(o,u,t,c-u+1)}l=_}}VM(t,i,s){if(null===this.Yt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="";const a=this.DM(e);for(let t=s.from;t<s.to;t++){const s=i[t];let l=Math.round(Math.min(s.jl,s.Zl)*r),o=Math.round(Math.max(s.jl,s.Zl)*r),_=Math.round(s._t*e)-Math.floor(.5*this.SM),u=_+this.SM-1;if(s.cr!==h){const t=s.cr;n.fillStyle=t,h=t}this.Yt.Mi&&(_+=a,l+=a,u-=a,o-=a),l>o||n.fillRect(_,l,u-_+1,o-l+1)}}}class xe extends ge{constructor(){super(...arguments),this.hg=new Se}jg(t,i,s){return{...this.yM(t,i,s),...s.Dr(t)}}cg(){const t=this.Jn.N();this.hg.ht({Xs:this.sg,vu:this.Qn.Et().vu(),TM:t.wickVisible,Mi:t.borderVisible,lt:this.ng})}}const Ce={type:"Candlestick",isBuiltIn:!0,defaultOptions:{upColor:"#26a69a",downColor:"#ef5350",wickVisible:!0,borderVisible:!0,borderColor:"#378658",borderUpColor:"#26a69a",borderDownColor:"#ef5350",wickColor:"#737375",wickUpColor:"#26a69a",wickDownColor:"#ef5350"},Fg:(t,i)=>new xe(t,i)};class Pe extends R{constructor(){super(...arguments),this.Yt=null,this.IM=[]}ht(t){this.Yt=t,this.IM=[]}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.Yt||0===this.Yt.ot.length||null===this.Yt.lt)return;this.IM.length||this.BM(i);const n=Math.max(1,Math.floor(s)),e=Math.round(this.Yt.EM*s)-Math.floor(n/2),r=e+n;for(let i=this.Yt.lt.from;i<this.Yt.lt.to;i++){const h=this.Yt.ot[i],a=this.IM[i-this.Yt.lt.from],l=Math.round(h.ut*s);let o,_;t.fillStyle=h.cr,l<=e?(o=l,_=r):(o=e,_=l-Math.floor(n/2)+n),t.fillRect(a.Uh,o,a.bi-a.Uh+1,_-o)}}BM(t){if(null===this.Yt||0===this.Yt.ot.length||null===this.Yt.lt)return void(this.IM=[]);const i=Math.ceil(this.Yt.vu*t)<=1?0:Math.max(1,Math.floor(t)),s=Math.round(this.Yt.vu*t)-i;this.IM=new Array(this.Yt.lt.to-this.Yt.lt.from);for(let i=this.Yt.lt.from;i<this.Yt.lt.to;i++){const n=this.Yt.ot[i],e=Math.round(n._t*t);let r,h;if(s%2){const t=(s-1)/2;r=e-t,h=e+t}else{const t=s/2;r=e-t,h=e+t-1}this.IM[i-this.Yt.lt.from]={Uh:r,bi:h,AM:e,ne:n._t*t,wt:n.wt}}for(let t=this.Yt.lt.from+1;t<this.Yt.lt.to;t++){const s=this.IM[t-this.Yt.lt.from],n=this.IM[t-this.Yt.lt.from-1];s.wt===n.wt+1&&(s.Uh-n.bi!==i+1&&(n.AM>n.ne?n.bi=s.Uh-i-1:s.Uh=n.bi+i+1))}let n=Math.ceil(this.Yt.vu*t);for(let t=this.Yt.lt.from;t<this.Yt.lt.to;t++){const i=this.IM[t-this.Yt.lt.from];i.bi<i.Uh&&(i.bi=i.Uh);const s=i.bi-i.Uh+1;n=Math.min(s,n)}if(i>0&&n<4)for(let t=this.Yt.lt.from;t<this.Yt.lt.to;t++){const i=this.IM[t-this.Yt.lt.from];i.bi-i.Uh+1>n&&(i.AM>i.ne?i.bi-=1:i.Uh+=1)}}}class ke extends Hn{constructor(){super(...arguments),this.hg=new Pe}jg(t,i,s){return{...this.Yg(t,i),...s.Dr(t)}}cg(){const t={ot:this.sg,vu:this.Qn.Et().vu(),lt:this.ng,EM:this.Jn.Ft().Nt(this.Jn.N().base,u(this.Jn.zt()).Wt)};this.hg.ht(t)}}const ye={type:"Histogram",isBuiltIn:!0,defaultOptions:{color:"#26a69a",base:0},Fg:(t,i)=>new ke(t,i)};class Te{constructor(t,i){this.Pt=t,this.zM=i,this.LM()}detach(){this.Pt.detachPrimitive(this.zM)}getPane(){return this.Pt}applyOptions(t){this.zM.hr?.(t)}LM(){this.Pt.attachPrimitive(this.zM)}}const Re={visible:!0,horzAlign:"center",vertAlign:"center",lines:[]},De={color:"rgba(0, 0, 0, 0.5)",fontSize:48,fontFamily:S,fontStyle:"",text:""};class Ve{constructor(t){this.OM=new Map,this.Yt=t}draw(t){t.useMediaCoordinateSpace((t=>{if(!this.Yt.visible)return;const{context:i,mediaSize:s}=t;let n=0;for(const t of this.Yt.lines){if(0===t.text.length)continue;i.font=t.k;const e=this.NM(i,t.text);e>s.width?t.xu=s.width/e:t.xu=1,n+=t.lineHeight*t.xu}let e=0;switch(this.Yt.vertAlign){case"top":e=0;break;case"center":e=Math.max((s.height-n)/2,0);break;case"bottom":e=Math.max(s.height-n,0)}for(const t of this.Yt.lines){i.save(),i.fillStyle=t.color;let n=0;switch(this.Yt.horzAlign){case"left":i.textAlign="left",n=t.lineHeight/2;break;case"center":i.textAlign="center",n=s.width/2;break;case"right":i.textAlign="right",n=s.width-1-t.lineHeight/2}i.translate(n,e),i.textBaseline="top",i.font=t.k,i.scale(t.xu,t.xu),i.fillText(t.text,0,t.FM),i.restore(),e+=t.lineHeight*t.xu}}))}NM(t,i){const s=this.WM(t.font);let n=s.get(i);return void 0===n&&(n=t.measureText(i).width,s.set(i,n)),n}WM(t){let i=this.OM.get(t);return void 0===i&&(i=new Map,this.OM.set(t,i)),i}}class Ie{constructor(t){this.Ps=Ee(t)}kt(t){this.Ps=Ee(t)}renderer(){return new Ve(this.Ps)}}function Be(t){return{...t,k:x(t.fontSize,t.fontFamily,t.fontStyle),lineHeight:t.lineHeight||1.2*t.fontSize,FM:0,xu:0}}function Ee(t){return{...t,lines:t.lines.map(Be)}}function Ae(t){return{...De,...t}}function ze(t){return{...Re,...t,lines:t.lines?.map(Ae)??[]}}class Le{constructor(t){this.Ps=ze(t),this.HM=[new Ie(this.Ps)]}updateAllViews(){this.HM.forEach((t=>t.kt(this.Ps)))}paneViews(){return this.HM}attached({requestUpdate:t}){this.UM=t}detached(){this.UM=void 0}hr(t){this.Ps=ze({...this.Ps,...t}),this.UM&&this.UM()}}function Oe(t,i){return new Te(t,new Le(i))}const Ne={alpha:1,padding:0};class Fe{constructor(t){this.Yt=t}draw(t){t.useMediaCoordinateSpace((t=>{const i=t.context,s=this.$M(this.Yt,t.mediaSize);s&&this.Yt.qM&&(i.globalAlpha=this.Yt.alpha??1,i.drawImage(this.Yt.qM,s._t,s.ut,s.Qi,s.$t))}))}$M(t,i){const{maxHeight:s,maxWidth:n,YM:e,jM:r,padding:h}=t,a=Math.round(i.width/2),l=Math.round(i.height/2),o=h??0;let _=i.width-2*o,u=i.height-2*o;s&&(u=Math.min(u,s)),n&&(_=Math.min(_,n));const c=_/r,d=u/e,f=Math.min(c,d),p=r*f,v=e*f;return{_t:a-.5*p,ut:l-.5*v,$t:v,Qi:p}}}class We{constructor(t){this.KM=null,this.XM=0,this.ZM=0,this.Ps=t,this.M=He(this.Ps,this.KM,this.XM,this.ZM)}GM(t){void 0!==t.JM&&(this.XM=t.JM),void 0!==t.QM&&(this.ZM=t.QM),void 0!==t.tb&&(this.KM=t.tb),this.kt()}ib(t){this.Ps=t,this.kt()}zOrder(){return"bottom"}kt(){this.M=He(this.Ps,this.KM,this.XM,this.ZM)}renderer(){return new Fe(this.M)}}function He(t,i,s,n){return{...t,qM:i,jM:s,YM:n}}function Ue(t){return{...Ne,...t}}class $e{constructor(t,i){this.sb=null,this.nb=t,this.Ps=Ue(i),this.HM=[new We(this.Ps)]}updateAllViews(){this.HM.forEach((t=>t.kt()))}paneViews(){return this.HM}attached(t){const{requestUpdate:i}=t;this.eb=i,this.sb=new Image,this.sb.onload=()=>{const t=this.sb?.naturalHeight??1,i=this.sb?.naturalWidth??1;this.HM.forEach((s=>s.GM({QM:t,JM:i,tb:this.sb}))),this.eb&&this.eb()},this.sb.src=this.nb}detached(){this.eb=void 0,this.sb=null}hr(t){this.Ps=Ue({...this.Ps,...t}),this.rb(),this.UM&&this.UM()}UM(){this.eb&&this.eb()}rb(){this.HM.forEach((t=>t.ib(this.Ps)))}}function qe(t,i,s){return new Te(t,new $e(i,s))}class Ye{constructor(t,i){this.Jn=t,this.ah=i,this.LM()}detach(){this.Jn.detachPrimitive(this.ah)}getSeries(){return this.Jn}applyOptions(t){this.ah&&this.ah.hr&&this.ah.hr(t)}LM(){this.Jn.attachPrimitive(this.ah)}}const je={zOrder:"normal"};function Ke(t,i){return Qt(Math.min(Math.max(t,12),30)*i)}function Xe(t,i){switch(t){case"arrowDown":case"arrowUp":return Ke(i,1);case"circle":return Ke(i,.8);case"square":return Ke(i,.7)}}function Ze(t){return function(t){const i=Math.ceil(t);return i%2!=0?i-1:i}(Ke(t,1))}function Ge(t){return Math.max(Ke(t,.1),3)}function Je(t,i,s){return i?t:s?Math.ceil(t/2):0}function Qe(t,i,s,n){const e=(Xe("arrowUp",n)-1)/2*s.hb,r=(Qt(n/2)-1)/2*s.hb;i.beginPath(),t?(i.moveTo(s._t-e,s.ut),i.lineTo(s._t,s.ut-e),i.lineTo(s._t+e,s.ut),i.lineTo(s._t+r,s.ut),i.lineTo(s._t+r,s.ut+e),i.lineTo(s._t-r,s.ut+e),i.lineTo(s._t-r,s.ut)):(i.moveTo(s._t-e,s.ut),i.lineTo(s._t,s.ut+e),i.lineTo(s._t+e,s.ut),i.lineTo(s._t+r,s.ut),i.lineTo(s._t+r,s.ut-e),i.lineTo(s._t-r,s.ut-e),i.lineTo(s._t-r,s.ut)),i.fill()}function tr(t,i,s,n,e,r){const h=(Xe("arrowUp",n)-1)/2,a=(Qt(n/2)-1)/2;if(e>=i-a-2&&e<=i+a+2&&r>=(t?s:s-h)-2&&r<=(t?s+h:s)+2)return!0;return(()=>{if(e<i-h-3||e>i+h+3||r<(t?s-h-3:s)||r>(t?s:s+h+3))return!1;const n=Math.abs(e-i);return Math.abs(r-s)+3>=n/2})()}class ir{constructor(){this.Yt=null,this.On=new rt,this.F=-1,this.W="",this.Fp="",this.ab="normal"}ht(t){this.Yt=t}Nn(t,i,s){this.F===t&&this.W===i||(this.F=t,this.W=i,this.Fp=x(t,i),this.On.In()),this.ab=s}jn(t,i){if(null===this.Yt||null===this.Yt.lt)return null;for(let s=this.Yt.lt.from;s<this.Yt.lt.to;s++){const n=this.Yt.ot[s];if(n&&nr(n,t,i))return{zOrder:"normal",externalId:n.Kn??""}}return null}draw(t){"aboveSeries"!==this.ab&&t.useBitmapCoordinateSpace((t=>{this.et(t)}))}drawBackground(t){"aboveSeries"===this.ab&&t.useBitmapCoordinateSpace((t=>{this.et(t)}))}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null!==this.Yt&&null!==this.Yt.lt){t.textBaseline="middle",t.font=this.Fp;for(let n=this.Yt.lt.from;n<this.Yt.lt.to;n++){const e=this.Yt.ot[n];void 0!==e.ri&&(e.ri.Qi=this.On.Vi(t,e.ri.lb),e.ri.$t=this.F,e.ri._t=e._t-e.ri.Qi/2),sr(e,t,i,s)}}}}function sr(t,i,s,n){i.fillStyle=t.R,void 0!==t.ri&&function(t,i,s,n,e,r){t.save(),t.scale(e,r),t.fillText(i,s,n),t.restore()}(i,t.ri.lb,t.ri._t,t.ri.ut,s,n),function(t,i,s){if(0===t.zr)return;switch(t.ob){case"arrowDown":return void Qe(!1,i,s,t.zr);case"arrowUp":return void Qe(!0,i,s,t.zr);case"circle":return void function(t,i,s){const n=(Xe("circle",s)-1)/2;t.beginPath(),t.arc(i._t,i.ut,n*i.hb,0,2*Math.PI,!1),t.fill()}(i,s,t.zr);case"square":return void function(t,i,s){const n=Xe("square",s),e=(n-1)*i.hb/2,r=i._t-e,h=i.ut-e;t.fillRect(r,h,n*i.hb,n*i.hb)}(i,s,t.zr)}t.ob}(t,i,function(t,i,s){const n=Math.max(1,Math.floor(i))%2/2;return{_t:Math.round(t._t*i)+n,ut:t.ut*s,hb:i}}(t,s,n))}function nr(t,i,s){return!(void 0===t.ri||!function(t,i,s,n,e,r){const h=n/2;return e>=t&&e<=t+s&&r>=i-h&&r<=i+h}(t.ri._t,t.ri.ut,t.ri.Qi,t.ri.$t,i,s))||function(t,i,s){if(0===t.zr)return!1;switch(t.ob){case"arrowDown":return tr(!0,t._t,t.ut,t.zr,i,s);case"arrowUp":return tr(!1,t._t,t.ut,t.zr,i,s);case"circle":return function(t,i,s,n,e){const r=2+Xe("circle",s)/2,h=t-n,a=i-e;return Math.sqrt(h*h+a*a)<=r}(t._t,t.ut,t.zr,i,s);case"square":return function(t,i,s,n,e){const r=Xe("square",s),h=(r-1)/2,a=t-h,l=i-h;return n>=a&&n<=a+r&&e>=l&&e<=l+r}(t._t,t.ut,t.zr,i,s)}}(t,i,s)}function er(t){return"atPriceTop"===t||"atPriceBottom"===t||"atPriceMiddle"===t}function rr(t,i,s,n,e,r,h,a){const l=function(t,i){if(er(i.position)&&void 0!==i.price)return i.price;if("value"in(s=t)&&"number"==typeof s.value)return t.value;var s;if(function(t){return"open"in t&&"high"in t&&"low"in t&&"close"in t}(t)){if("inBar"===i.position)return t.close;if("aboveBar"===i.position)return t.high;if("belowBar"===i.position)return t.low}}(s,i);if(void 0===l)return;const o=er(i.position),_=a.timeScale(),c=p(i.size)?Math.max(i.size,0):1,d=Ze(_.options().barSpacing)*c,f=d/2;t.zr=d;switch(i.position){case"inBar":case"atPriceMiddle":return t.ut=u(h.priceToCoordinate(l)),void(void 0!==t.ri&&(t.ri.ut=t.ut+f+r+.6*e));case"aboveBar":case"atPriceTop":{const i=o?0:n._b;return t.ut=u(h.priceToCoordinate(l))-f-i,void 0!==t.ri&&(t.ri.ut=t.ut-f-.6*e,n._b+=1.2*e),void(o||(n._b+=d+r))}case"belowBar":case"atPriceBottom":{const i=o?0:n.ub;return t.ut=u(h.priceToCoordinate(l))+f+i,void 0!==t.ri&&(t.ri.ut=t.ut+f+r+.6*e,n.ub+=1.2*e),void(o||(n.ub+=d+r))}}}class hr{constructor(t,i,s){this.cb=[],this.xt=!0,this.fb=!0,this.Gt=new ir,this.ge=t,this.Dp=i,this.Yt={ot:[],lt:null},this.Ps=s}renderer(){if(!this.ge.options().visible)return null;this.xt&&this.pb();const t=this.Dp.options().layout;return this.Gt.Nn(t.fontSize,t.fontFamily,this.Ps.zOrder),this.Gt.ht(this.Yt),this.Gt}mb(t){this.cb=t,this.kt("data")}kt(t){this.xt=!0,"data"===t&&(this.fb=!0)}wb(t){this.xt=!0,this.Ps=t}zOrder(){return"aboveSeries"===this.Ps.zOrder?"top":this.Ps.zOrder}pb(){const t=this.Dp.timeScale(),i=this.cb;this.fb&&(this.Yt.ot=i.map((t=>({wt:t.time,_t:0,ut:0,zr:0,ob:t.shape,R:t.color,Kn:t.id,gb:t.gb,ri:void 0}))),this.fb=!1);const s=this.Dp.options().layout;this.Yt.lt=null;const n=t.getVisibleLogicalRange();if(null===n)return;const e=new yi(Math.floor(n.from),Math.ceil(n.to));if(null===this.ge.data()[0])return;if(0===this.Yt.ot.length)return;let r=NaN;const h=Ge(t.options().barSpacing),a={_b:h,ub:h};this.Yt.lt=an(this.Yt.ot,e,!0);for(let n=this.Yt.lt.from;n<this.Yt.lt.to;n++){const e=i[n];e.time!==r&&(a._b=h,a.ub=h,r=e.time);const l=this.Yt.ot[n];l._t=u(t.logicalToCoordinate(e.time)),void 0!==e.text&&e.text.length>0&&(l.ri={lb:e.text,_t:0,ut:0,Qi:0,$t:0});const o=this.ge.dataByIndex(e.time,0);null!==o&&rr(l,e,o,a,s.fontSize,h,this.ge,this.Dp)}this.xt=!1}}function ar(t){return{...je,...t}}class lr{constructor(t){this.sh=null,this.cb=[],this.Mb=[],this.bb=null,this.ge=null,this.Dp=null,this.Sb=!0,this.xb=null,this.Cb=null,this.Pb=null,this.kb=!0,this.Ps=ar(t)}attached(t){this.yb(),this.Dp=t.chart,this.ge=t.series,this.sh=new hr(this.ge,u(this.Dp),this.Ps),this.eb=t.requestUpdate,this.ge.subscribeDataChanged((t=>this.Pg(t))),this.kb=!0,this.UM()}UM(){this.eb&&this.eb()}detached(){this.ge&&this.bb&&this.ge.unsubscribeDataChanged(this.bb),this.Dp=null,this.ge=null,this.sh=null,this.bb=null}mb(t){this.kb=!0,this.cb=t,this.yb(),this.Sb=!0,this.Cb=null,this.UM()}Tb(){return this.cb}paneViews(){return this.sh?[this.sh]:[]}updateAllViews(){this.Rb()}hitTest(t,i){return this.sh?this.sh.renderer()?.jn(t,i)??null:null}autoscaleInfo(t,i){if(this.sh){const t=this.Db();if(t)return{priceRange:null,margins:t}}return null}hr(t){this.Ps=ar({...this.Ps,...t}),this.UM&&this.UM()}Db(){const t=u(this.Dp).timeScale().options().barSpacing;if(this.Sb||t!==this.Pb){if(this.Pb=t,this.cb.length>0){const i=Ge(t),s=1.5*Ze(t)+2*i,n=this.Vb();this.xb={above:Je(s,n.aboveBar,n.inBar),below:Je(s,n.belowBar,n.inBar)}}else this.xb=null;this.Sb=!1}return this.xb}Vb(){return null===this.Cb&&(this.Cb=this.cb.reduce(((t,i)=>(t[i.position]||(t[i.position]=!0),t)),{inBar:!1,aboveBar:!1,belowBar:!1,atPriceTop:!1,atPriceBottom:!1,atPriceMiddle:!1})),this.Cb}yb(){if(!this.kb||!this.Dp||!this.ge)return;const t=this.Dp.timeScale(),i=this.ge?.data();if(null==t.getVisibleLogicalRange()||!this.ge||0===i.length)return void(this.Mb=[]);const s=t.timeToIndex(u(i[0].time),!0);this.Mb=this.cb.map(((i,n)=>{const e=t.timeToIndex(i.time,!0),r=e<s?1:-1,h=u(this.ge).dataByIndex(e,r),a={time:t.timeToIndex(u(h).time,!1),position:i.position,shape:i.shape,color:i.color,id:i.id,gb:n,text:i.text,size:i.size,price:i.price,Pw:i.time};if("atPriceTop"===i.position||"atPriceBottom"===i.position||"atPriceMiddle"===i.position){if(void 0===i.price)throw new Error(`Price is required for position ${i.position}`);return{...a,position:i.position,price:i.price}}return{...a,position:i.position,price:i.price}})),this.kb=!1}Rb(t){this.sh&&(this.yb(),this.sh.mb(this.Mb),this.sh.wb(this.Ps),this.sh.kt(t))}Pg(t){this.kb=!0,this.UM()}}class or extends Ye{constructor(t,i,s){super(t,i),s&&this.setMarkers(s)}setMarkers(t){this.ah.mb(t)}markers(){return this.ah.Tb()}}function _r(t,i,s){const n=new or(t,new lr(s??{}));return i&&n.setMarkers(i),n}class ur{constructor(t){this.cb=new Map,this.Ib=t}Bb(t,i,s){if(this.Eb(i),void 0!==s){const n=window.setTimeout((()=>{this.cb.delete(i),this.Ab()}),s),e={...t,zb:n,Lb:Date.now()+s};this.cb.set(i,e)}else this.cb.set(i,{...t,zb:void 0,Lb:void 0});this.Ab()}Eb(t){const i=this.cb.get(t);i&&void 0!==i.zb&&window.clearTimeout(i.zb),this.cb.delete(t),this.Ab()}Ob(){for(const[t]of this.cb)this.Eb(t)}Nb(){const t=Date.now(),i=[];for(const[s,n]of this.cb)!n.Lb||n.Lb>t?i.push({time:n.time,sign:n.sign,value:n.value}):this.Eb(s);return i}Fb(t){this.Ib=t}Ab(){this.Ib&&this.Ib()}}const cr={positiveColor:"#22AB94",negativeColor:"#F7525F",updateVisibilityDuration:5e3};class dr{constructor(t,i,s,n){this.Yt=t,this.Wb=i,this.Hb=s,this.Ub=n}draw(t){t.useBitmapCoordinateSpace((t=>{const i=t.context,s=Math.max(1,Math.floor(t.horizontalPixelRatio))%2/2,n=4*t.verticalPixelRatio+s;this.Yt.forEach((e=>{const r=Math.round(e._t*t.horizontalPixelRatio)+s;i.beginPath();const h=this.$b(e.qb);i.fillStyle=h,i.arc(r,e.ut*t.verticalPixelRatio,n,0,2*Math.PI,!1),i.fill(),e.qb&&(i.strokeStyle=h,i.lineWidth=Math.floor(2*t.horizontalPixelRatio),i.beginPath(),i.moveTo((e._t-4.7)*t.horizontalPixelRatio+s,(e.ut-7*e.qb)*t.verticalPixelRatio),i.lineTo(e._t*t.horizontalPixelRatio+s,(e.ut-7*e.qb-7*e.qb*.5)*t.verticalPixelRatio),i.lineTo((e._t+4.7)*t.horizontalPixelRatio+s,(e.ut-7*e.qb)*t.verticalPixelRatio),i.stroke())}))}))}$b(t){return 0===t?this.Wb:t>0?this.Ub:this.Hb}}class fr{constructor(t,i,s){this.Yt=[],this.ge=t,this.uh=i,this.Ps=s}kt(t){this.Yt=t.map((t=>{const i=this.ge.priceToCoordinate(t.value);if(null===i)return null;return{_t:u(this.uh.timeToCoordinate(t.time)),ut:i,qb:t.sign}})).filter(M)}renderer(){const t=function(t,i){return function(t,i){return"Area"===i}(0,i)?t.lineColor:t.color}(this.ge.options(),this.ge.seriesType());return new dr(this.Yt,t,this.Ps.negativeColor,this.Ps.positiveColor)}}function pr(t,i){return"Line"===i||"Area"===i}class vr{constructor(t){this.Dp=void 0,this.ge=void 0,this.HM=[],this.o_=null,this.Yb=new Map,this.jb=new ur((()=>this.UM())),this.Ps={...cr,...t}}hr(t){this.Ps={...this.Ps,...t},this.UM()}mb(t){this.jb.Ob();const i=this.o_;i&&t.forEach((t=>{this.jb.Bb(t,i.key(t.time))}))}Tb(){return this.jb.Nb()}UM(){this.eb?.()}attached(t){const{chart:i,series:s,requestUpdate:n,horzScaleBehavior:e}=t;this.Dp=i,this.ge=s,this.o_=e;const r=this.ge.seriesType();if("Area"!==r&&"Line"!==r)throw new Error("UpDownMarkersPrimitive is only supported for Area and Line series types");this.HM=[new fr(this.ge,this.Dp.timeScale(),this.Ps)],this.eb=n,this.UM()}detached(){this.Dp=void 0,this.ge=void 0,this.eb=void 0}$p(){return _(this.Dp)}Oo(){return _(this.ge)}updateAllViews(){this.HM.forEach((t=>t.kt(this.Tb())))}paneViews(){return this.HM}ht(t){if(!this.ge)throw new Error("Primitive not attached to series");const i=this.ge.seriesType();this.Yb.clear();const s=this.o_;s&&t.forEach((t=>{Hs(t)&&pr(0,i)&&this.Yb.set(s.key(t.time),t.value)})),_(this.ge).setData(t)}kt(t,i){if(!this.ge||!this.o_)throw new Error("Primitive not attached to series");const s=this.ge.seriesType(),n=this.o_.key(t.time);if(Ws(t)&&this.Yb.delete(n),Hs(t)&&pr(0,s)){const i=this.Yb.get(n);i&&this.jb.Bb({time:t.time,value:t.value,sign:mr(t.value,i)},n,this.Ps.updateVisibilityDuration)}_(this.ge).update(t,i)}Kb(){this.jb.Ob()}}function mr(t,i){return t===i?0:t-i>0?1:-1}class wr extends Ye{setData(t){return this.ah.ht(t)}update(t,i){return this.ah.kt(t,i)}markers(){return this.ah.Tb()}setMarkers(t){return this.ah.mb(t)}clearMarkers(){return this.ah.Kb()}}function gr(t,i={}){return new wr(t,new vr(i))}const Mr={...e,color:"#2196f3"};function br(){return"5.0.8"}export{me as AreaSeries,be as BarSeries,fe as BaselineSeries,Ce as CandlestickSeries,zi as ColorType,K as CrosshairMode,ye as HistogramSeries,Ei as LastPriceAnimationMode,Jn as LineSeries,h as LineStyle,r as LineType,Tt as MismatchDirection,Ai as PriceLineSource,vi as PriceScaleMode,Li as TickMarkType,Bi as TrackingModeExitMode,Fn as createChart,Nn as createChartEx,qe as createImageWatermark,ae as createOptionsChart,_r as createSeriesMarkers,Oe as createTextWatermark,gr as createUpDownMarkers,ee as createYieldCurveChart,Mr as customSeriesDefaultOptions,Wn as defaultHorzScaleBehavior,Ni as isBusinessDay,Fi as isUTCTimestamp,br as version};
