{"version": "5.0.8", "name": "lightweight-charts", "author": "TradingView, Inc.", "license": "Apache-2.0", "description": "Performant financial charts built with HTML5 canvas", "homepage": "https://www.tradingview.com/lightweight-charts/", "bugs": {"url": "https://github.com/tradingview/lightweight-charts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/tradingview/lightweight-charts.git"}, "type": "module", "module": "dist/lightweight-charts.production.mjs", "main": "dist/lightweight-charts.production.mjs", "typings": "dist/typings.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": {"types": "./dist/typings.d.ts", "import": "./dist/lightweight-charts.development.mjs"}, "production": {"types": "./dist/typings.d.ts", "import": "./dist/lightweight-charts.production.mjs"}, "default": {"types": "./dist/typings.d.ts", "import": "./dist/lightweight-charts.production.mjs"}}}, "files": ["dist/**"], "keywords": ["financial-charting-library", "charting-library", "html5-charts", "canvas", "typescript", "charting", "charts", "tradingview", "candlestick"], "dependencies": {"fancy-canvas": "2.1.0"}}