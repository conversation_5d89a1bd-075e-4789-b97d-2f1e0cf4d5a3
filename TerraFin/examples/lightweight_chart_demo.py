"""
TerraFin Lightweight Chart Demo

This example demonstrates how to use the new TerraFinChart class
that provides web-compatible interactive charts using Streamlit components.

Run with:
streamlit run TerraFin/examples/lightweight_chart_demo.py
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add TerraFin to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from TerraFin.visualization import TerraFin<PERSON>hart
from TerraFin.data_factory import DataFactory

def generate_sample_ohlc_data(days=100):
    """Generate sample OHLCV data for demonstration"""
    dates = []
    data = []
    
    base_date = datetime(2023, 1, 1)
    base_price = 100.0
    
    for i in range(days):
        date = base_date + timedelta(days=i)
        
        # Simple random walk for price
        change = np.random.normal(0, 2)  # Random change with std dev of 2
        base_price = max(10, base_price + change)  # Ensure price stays positive
        
        # Generate OHLC data
        open_price = base_price
        high_price = open_price + abs(np.random.normal(0, 1))
        low_price = open_price - abs(np.random.normal(0, 1))
        close_price = open_price + change
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'time': date.strftime('%Y-%m-%d'),
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': volume
        })
        
        base_price = close_price
    
    return pd.DataFrame(data)

def generate_line_data(days=100):
    """Generate sample line data"""
    dates = []
    values = []
    
    base_date = datetime(2023, 1, 1)
    base_value = 50.0
    
    for i in range(days):
        date = base_date + timedelta(days=i)
        base_value += np.random.normal(0, 1)
        
        dates.append(date.strftime('%Y-%m-%d'))
        values.append(round(base_value, 2))
    
    return pd.DataFrame({
        'time': dates,
        'value': values
    })

def main():
    st.set_page_config(
        page_title="TerraFin Lightweight Chart Demo",
        page_icon="📈",
        layout="wide"
    )
    
    st.title("📈 TerraFin Lightweight Chart Demo")
    st.markdown("""
    This demo showcases the new **TerraFinChart** class that provides interactive financial charts
    using the official lightweight-charts library in Streamlit applications.
    
    **Key Features:**
    - ✅ Uses official `lightweight-charts` npm package (v5.0.8)
    - ✅ Web-compatible (no pywebview dependency)
    - ✅ Multiple chart types (Candlestick, Line, Area, Histogram)
    - ✅ Streamlit integration
    - ✅ Customizable themes and options
    """)
    
    # Sidebar controls
    st.sidebar.header("Chart Configuration")
    
    # Chart type selection
    chart_type = st.sidebar.selectbox(
        "Chart Type",
        ["candlestick", "line", "area", "histogram"],
        index=0
    )
    
    # Chart dimensions
    width = st.sidebar.slider("Chart Width", 400, 1200, 800)
    height = st.sidebar.slider("Chart Height", 300, 800, 600)
    
    # Theme selection
    theme = st.sidebar.selectbox("Theme", ["dark", "light"], index=0)
    
    # Data source
    data_source = st.sidebar.selectbox(
        "Data Source",
        ["Sample Data", "Real Market Data (if available)"],
        index=0
    )
    
    # Generate or load data
    if data_source == "Sample Data":
        if chart_type == "candlestick":
            df = generate_sample_ohlc_data()
            st.sidebar.success(f"Generated {len(df)} candlestick data points")
        elif chart_type == "histogram":
            df = generate_sample_ohlc_data()
            # Use volume data for histogram
            df = df[['time', 'volume']].rename(columns={'volume': 'value'})
            st.sidebar.success(f"Generated {len(df)} volume data points")
        else:
            df = generate_line_data()
            st.sidebar.success(f"Generated {len(df)} line data points")
    else:
        # Try to load real market data
        try:
            factory = DataFactory()
            df = factory.get_market_data("AAPL")  # Example with Apple stock
            if chart_type != "candlestick":
                df = df[['time', 'close']].rename(columns={'close': 'value'})
            st.sidebar.success(f"Loaded {len(df)} real market data points")
        except Exception as e:
            st.sidebar.error(f"Could not load real data: {e}")
            st.sidebar.info("Falling back to sample data")
            df = generate_sample_ohlc_data() if chart_type == "candlestick" else generate_line_data()
    
    # Display data preview
    with st.expander("Data Preview"):
        st.dataframe(df.head(10))
        st.write(f"**Total rows:** {len(df)}")
        st.write(f"**Columns:** {list(df.columns)}")
    
    # Create and display the chart
    st.header("Interactive Chart")
    
    try:
        # Create the chart
        chart = TerraFinChart(
            width=width,
            height=height,
            theme=theme,
            key=f"chart_{chart_type}_{theme}"
        )
        
        # Set the data and chart type
        chart.set(df, chart_type=chart_type)
        
        # Show the chart
        component_value = chart.show()
        
        # Display any interactions
        if component_value:
            st.subheader("Chart Interactions")
            st.json(component_value)
            
    except Exception as e:
        st.error(f"Error creating chart: {e}")
        st.info("""
        **Troubleshooting:**
        
        1. Make sure the TerraFin component is built:
           ```bash
           cd TerraFin/src/TerraFin/visualization/lightweight_chart
           ./build_component.sh
           ```
        
        2. For development mode, set `_RELEASE = False` in the TerraFinChart class
           and run the React dev server:
           ```bash
           cd frontend
           npm start
           ```
        """)
    
    # Usage examples
    st.header("Usage Examples")
    
    st.subheader("Basic Usage")
    st.code("""
from TerraFin.visualization import TerraFinChart
import pandas as pd

# Create sample data
df = pd.DataFrame({
    'time': ['2023-01-01', '2023-01-02', '2023-01-03'],
    'open': [100, 102, 101],
    'high': [105, 106, 104],
    'low': [98, 100, 99],
    'close': [102, 101, 103]
})

# Create and show chart
chart = TerraFinChart(width=800, height=600, theme='dark')
chart.set(df, chart_type='candlestick')
chart.show()
""", language='python')
    
    st.subheader("Different Chart Types")
    st.code("""
# Candlestick chart (requires: time, open, high, low, close)
chart.set(ohlc_df, chart_type='candlestick')

# Line chart (requires: time, value)
chart.set(line_df, chart_type='line')

# Area chart (requires: time, value)
chart.set(area_df, chart_type='area')

# Histogram chart (requires: time, value)
chart.set(volume_df, chart_type='histogram')
""", language='python')
    
    st.subheader("Customization")
    st.code("""
# Custom chart options
chart = TerraFinChart(
    width=1000,
    height=700,
    theme='light',
    key='my_custom_chart'
)

# Set additional options
chart.set_options(
    grid={'vertLines': {'color': '#e1e3e6'}},
    crosshair={'mode': 1}
)
""", language='python')

if __name__ == "__main__":
    main()
