{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# TerraFin Chart - Jupyter Notebook Example\n", "\n", "This notebook demonstrates how to use TerraFinChart in Jupyter notebooks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "from TerraFin.visualization.lightweight_chart import TerraFinChart"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Sample Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_sample_data():\n", "    \"\"\"Create sample OHLCV data for demonstration.\"\"\"\n", "    # Generate sample data\n", "    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')\n", "    np.random.seed(42)\n", "    \n", "    # Generate realistic price data\n", "    base_price = 100\n", "    prices = []\n", "    current_price = base_price\n", "    \n", "    for _ in dates:\n", "        # Random walk with slight upward bias\n", "        change = np.random.normal(0.001, 0.02)  # 0.1% daily drift, 2% volatility\n", "        current_price *= (1 + change)\n", "        prices.append(current_price)\n", "    \n", "    # Create OHLCV data\n", "    data = []\n", "    for i, (date, close) in enumerate(zip(dates, prices)):\n", "        # Generate realistic OHLC from close price\n", "        volatility = close * 0.01  # 1% intraday volatility\n", "        high = close + np.random.uniform(0, volatility)\n", "        low = close - np.random.uniform(0, volatility)\n", "        open_price = low + np.random.uniform(0, high - low)\n", "        volume = np.random.randint(1000, 10000)\n", "        \n", "        data.append({\n", "            'time': date,\n", "            'open': round(open_price, 2),\n", "            'high': round(high, 2),\n", "            'low': round(low, 2),\n", "            'close': round(close, 2),\n", "            'volume': volume\n", "        })\n", "    \n", "    return pd.DataFrame(data)\n", "\n", "# Create the sample data\n", "df = create_sample_data()\n", "print(f\"Created {len(df)} data points\")\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Candlestick Chart"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create candlestick chart\n", "chart = <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    width=800, \n", "    height=400, \n", "    theme=\"dark\", \n", "    environment=\"jupyter\"\n", ")\n", "\n", "chart.set_chart_type(\"candlestick\")\n", "chart.set(df)\n", "chart.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Line Chart"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create line chart (using only close prices)\n", "line_data = df[['time', 'close']].copy()\n", "line_data = line_data.rename(columns={'close': 'value'})\n", "\n", "chart_line = TerraFin<PERSON>hart(\n", "    width=800, \n", "    height=400, \n", "    theme=\"light\", \n", "    environment=\"jupyter\"\n", ")\n", "\n", "chart_line.set_chart_type(\"line\")\n", "chart_line.set(line_data)\n", "chart_line.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Area Chart"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create area chart\n", "chart_area = TerraFinChart(\n", "    width=800, \n", "    height=400, \n", "    theme=\"dark\", \n", "    environment=\"jupyter\"\n", ")\n", "\n", "chart_area.set_chart_type(\"area\")\n", "chart_area.set(line_data)\n", "chart_area.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Volume Histogram"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create volume histogram\n", "volume_data = df[['time', 'volume']].copy()\n", "volume_data = volume_data.rename(columns={'volume': 'value'})\n", "\n", "chart_volume = TerraFinChart(\n", "    width=800, \n", "    height=300, \n", "    theme=\"dark\", \n", "    environment=\"jupyter\"\n", ")\n", "\n", "chart_volume.set_chart_type(\"histogram\")\n", "chart_volume.set(volume_data)\n", "chart_volume.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}