"""
Example demonstrating how to use TerraFinChart in both Streamlit and Jupyter environments.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from TerraFin.visualization.lightweight_chart import TerraFinChart

def create_sample_data():
    """Create sample OHLCV data for demonstration."""
    # Generate sample data
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 100
    prices = []
    current_price = base_price
    
    for _ in dates:
        # Random walk with slight upward bias
        change = np.random.normal(0.001, 0.02)  # 0.1% daily drift, 2% volatility
        current_price *= (1 + change)
        prices.append(current_price)
    
    # Create OHLCV data
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # Generate realistic OHLC from close price
        volatility = close * 0.01  # 1% intraday volatility
        high = close + np.random.uniform(0, volatility)
        low = close - np.random.uniform(0, volatility)
        open_price = low + np.random.uniform(0, high - low)
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'time': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)

def streamlit_example():
    """Example for Streamlit environment."""
    import streamlit as st
    
    st.title("TerraFin Chart - Streamlit Example")
    
    # Create sample data
    df = create_sample_data()
    
    # Chart configuration
    col1, col2, col3 = st.columns(3)
    with col1:
        chart_type = st.selectbox("Chart Type", ["candlestick", "line", "area", "histogram"])
    with col2:
        theme = st.selectbox("Theme", ["dark", "light"])
    with col3:
        height = st.slider("Height", 400, 800, 600)
    
    # Create and display chart
    chart = TerraFinChart(
        width=800, 
        height=height, 
        theme=theme, 
        environment="streamlit"
    )
    
    chart.set_chart_type(chart_type)
    chart.set(df)
    chart.show()
    
    # Show data info
    st.subheader("Data Info")
    st.write(f"Data points: {len(df)}")
    st.write(f"Date range: {df['time'].min()} to {df['time'].max()}")
    
    # Show sample data
    with st.expander("Sample Data"):
        st.dataframe(df.head(10))

def jupyter_example():
    """Example for Jupyter notebook environment."""
    print("TerraFin Chart - Jupyter Example")
    print("=" * 40)
    
    # Create sample data
    df = create_sample_data()
    
    # Create different chart types
    chart_types = ["candlestick", "line", "area"]
    
    for chart_type in chart_types:
        print(f"\n{chart_type.title()} Chart:")
        print("-" * 20)
        
        # Create and display chart
        chart = TerraFinChart(
            width=800, 
            height=400, 
            theme="dark", 
            environment="jupyter"
        )
        
        chart.set_chart_type(chart_type)
        chart.set(df)
        chart.show()
    
    print(f"\nData Info:")
    print(f"Data points: {len(df)}")
    print(f"Date range: {df['time'].min()} to {df['time'].max()}")

if __name__ == "__main__":
    # This will run the Streamlit example if executed directly
    # For Jupyter, call jupyter_example() in a cell
    streamlit_example()
