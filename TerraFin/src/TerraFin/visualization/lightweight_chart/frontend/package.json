{"name": "terrafin-lightweight-chart", "version": "0.1.0", "private": true, "dependencies": {"react": "^16.13.1", "react-dom": "^16.13.1", "streamlit-component-lib": "^2.0.0", "lightweight-charts": "^5.0.8"}, "scripts": {"start": "PORT=3003 BROWSER=none react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": ".", "devDependencies": {"@types/node": "^12.0.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "react-scripts": "^5.0.1", "typescript": "^4.2.0"}}