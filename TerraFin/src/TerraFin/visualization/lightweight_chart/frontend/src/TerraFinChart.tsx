import React, { useEffect, useRef } from "react"
import {
  Streamlit,
  StreamlitComponentBase,
  withStreamlitConnection,
} from "streamlit-component-lib"
import {
  createChart,
  IChartApi,
  ISeriesApi,
  CandlestickData,
  LineData,
  HistogramData,
  ColorType,
  CrosshairMode,
  CandlestickSeries,
  LineSeries,
  AreaSeries,
  HistogramSeries
} from "lightweight-charts"

interface ChartProps {
  data: any[]
  chart_type: string
  width: number
  height: number
  theme: string
  options: any
}

class TerraFinChart extends StreamlitComponentBase<{}> {
  private chartContainerRef = React.createRef<HTMLDivElement>()
  private chart: IChartApi | null = null
  private series: ISeriesApi<any> | null = null

  public componentDidMount(): void {
    this.createChart()
    Streamlit.setFrameHeight(this.props.args.height || 600)
  }

  public componentDidUpdate(): void {
    this.updateChart()
  }

  public componentWillUnmount(): void {
    if (this.chart) {
      this.chart.remove()
    }
  }

  private createChart = (): void => {
    if (!this.chartContainerRef.current) return

    const args = this.props.args as ChartProps
    
    // Create the chart
    this.chart = createChart(this.chartContainerRef.current, {
      width: args.width || 800,
      height: args.height || 600,
      layout: {
        background: {
          type: ColorType.Solid,
          color: args.theme === 'dark' ? '#1e1e1e' : '#ffffff',
        },
        textColor: args.theme === 'dark' ? '#d1d4dc' : '#191919',
      },
      grid: {
        vertLines: {
          color: args.theme === 'dark' ? '#2B2B43' : '#e1e3e6',
        },
        horzLines: {
          color: args.theme === 'dark' ? '#2B2B43' : '#e1e3e6',
        },
      },
      crosshair: {
        mode: CrosshairMode.Normal,
      },
      rightPriceScale: {
        borderColor: args.theme === 'dark' ? '#2B2B43' : '#e1e3e6',
      },
      timeScale: {
        borderColor: args.theme === 'dark' ? '#2B2B43' : '#e1e3e6',
        timeVisible: true,
        secondsVisible: false,
      },
      ...args.options
    })

    this.addSeries()
    this.setData()

    // Handle resize
    const resizeObserver = new ResizeObserver(() => {
      if (this.chart && this.chartContainerRef.current) {
        this.chart.applyOptions({
          width: this.chartContainerRef.current.clientWidth,
          height: args.height || 600,
        })
      }
    })

    if (this.chartContainerRef.current) {
      resizeObserver.observe(this.chartContainerRef.current)
    }

    Streamlit.setComponentReady()
  }

  private addSeries = (): void => {
    if (!this.chart) return

    const args = this.props.args as ChartProps
    const isDark = args.theme === 'dark'

    switch (args.chart_type) {
      case 'candlestick':
        this.series = this.chart.addSeries(CandlestickSeries, {
          upColor: '#26a69a',
          downColor: '#ef5350',
          borderVisible: false,
          wickUpColor: '#26a69a',
          wickDownColor: '#ef5350',
        })
        break

      case 'line':
        this.series = this.chart.addSeries(LineSeries, {
          color: '#2196F3',
          lineWidth: 2,
        })
        break

      case 'area':
        this.series = this.chart.addSeries(AreaSeries, {
          topColor: 'rgba(33, 150, 243, 0.56)',
          bottomColor: 'rgba(33, 150, 243, 0.04)',
          lineColor: 'rgba(33, 150, 243, 1)',
          lineWidth: 2,
        })
        break

      case 'histogram':
        this.series = this.chart.addSeries(HistogramSeries, {
          color: '#26a69a',
          priceFormat: {
            type: 'volume',
          },
          priceScaleId: '',
        })
        break

      default:
        this.series = this.chart.addSeries(LineSeries, {
          color: '#2196F3',
          lineWidth: 2,
        })
    }
  }

  private setData = (): void => {
    if (!this.series) return

    const args = this.props.args as ChartProps
    
    if (args.data && args.data.length > 0) {
      this.series.setData(args.data)
      
      // Fit content to show all data
      this.chart?.timeScale().fitContent()
    }
  }

  private updateChart = (): void => {
    if (!this.chart || !this.series) return

    const args = this.props.args as ChartProps
    
    // Update data if it has changed
    if (args.data && args.data.length > 0) {
      this.series.setData(args.data)
      this.chart.timeScale().fitContent()
    }

    // Update chart size if needed
    this.chart.applyOptions({
      width: args.width || 800,
      height: args.height || 600,
    })
  }

  public render = (): React.ReactNode => {
    const args = this.props.args as ChartProps
    
    return (
      <div
        ref={this.chartContainerRef}
        style={{
          width: args.width || 800,
          height: args.height || 600,
          position: 'relative'
        }}
      />
    )
  }
}

export default withStreamlitConnection(TerraFinChart)
