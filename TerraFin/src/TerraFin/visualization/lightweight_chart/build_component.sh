#!/usr/bin/env bash

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
CYAN='\033[0;36m'
NC='\033[0m'

ERROR="${RED}[ERROR]${NC} "
INFO="${CYAN}[INFO]${NC} "
WARNING="${YELLOW}[WARNING]${NC} "

SCRIPT_DIR=$(dirname "$0")
COMPONENT_DIR="${SCRIPT_DIR}/frontend"

echo -e "${INFO}Building TerraFin Lightweight Chart component..."
cd "$COMPONENT_DIR"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo -e "${INFO}Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${ERROR}Failed to install dependencies"
        exit 1
    fi
fi

# Build the component
echo -e "${INFO}Building React component..."
npm run build
if [ $? -ne 0 ]; then
    echo -e "${ERROR}Failed to build React component"
    exit 1
fi

echo -e "${GREEN}[BUILD SUCCESS]${NC} TerraFin Lightweight Chart component built successfully!"
echo -e "${INFO}Built files are in: $COMPONENT_DIR/build"

cd - > /dev/null
