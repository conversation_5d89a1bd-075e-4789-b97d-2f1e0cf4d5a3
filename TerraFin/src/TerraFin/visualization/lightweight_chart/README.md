# TerraFin Lightweight Charts

A dual-environment lightweight charts implementation that works in both Streamlit applications and Jupyter notebooks without requiring pywebview or desktop dependencies.

## Features

- **Dual Environment Support**: Works seamlessly in both Streamlit and Jupyter notebooks
- **Multiple Chart Types**: Candlestick, Line, Area, and Histogram charts
- **Customizable Themes**: Dark and light themes
- **Interactive**: Full interactivity with pan, zoom, and crosshair
- **No Desktop Dependencies**: Pure web-based implementation

## Installation

The chart component requires the following dependencies:

```bash
# For Streamlit support
pip install streamlit

# For Jupyter support  
pip install ipython jupyter

# Core dependency
pip install lightweight-charts
```

## Usage

### Basic Usage

```python
from TerraFin.visualization.lightweight_chart import TerraFinChart
import pandas as pd

# Create sample data
data = pd.DataFrame({
    'time': pd.date_range('2023-01-01', periods=100, freq='D'),
    'open': [100 + i for i in range(100)],
    'high': [105 + i for i in range(100)],
    'low': [95 + i for i in range(100)],
    'close': [102 + i for i in range(100)],
})

# Create chart for Streamlit
chart = TerraFinChart(
    width=800, 
    height=600, 
    theme="dark", 
    environment="streamlit"
)

chart.set(data)
chart.show()
```

### Streamlit Environment

```python
import streamlit as st
from TerraFin.visualization.lightweight_chart import TerraFinChart

st.title("Financial Chart")

# Create chart
chart = TerraFinChart(
    width=800, 
    height=600, 
    theme="dark", 
    environment="streamlit",
    key="my_chart"  # Optional: for state management
)

chart.set_chart_type("candlestick")
chart.set(your_dataframe)
result = chart.show()  # Returns interaction data
```

### Jupyter Notebook Environment

```python
from TerraFin.visualization.lightweight_chart import TerraFinChart

# Create chart
chart = TerraFinChart(
    width=800, 
    height=400, 
    theme="dark", 
    environment="jupyter"
)

chart.set_chart_type("line")
chart.set(your_dataframe)
chart.show()  # Displays inline in notebook
```

## Chart Types

### Candlestick Chart
Requires columns: `time`, `open`, `high`, `low`, `close`

```python
chart.set_chart_type("candlestick")
```

### Line Chart
Requires columns: `time`, `value`

```python
chart.set_chart_type("line")
```

### Area Chart
Requires columns: `time`, `value`

```python
chart.set_chart_type("area")
```

### Histogram Chart
Requires columns: `time`, `value`

```python
chart.set_chart_type("histogram")
```

## Configuration Options

### Constructor Parameters

- `width` (int): Chart width in pixels (default: 800)
- `height` (int): Chart height in pixels (default: 600)
- `theme` (str): Theme ("dark" or "light", default: "dark")
- `environment` (str): Target environment ("streamlit" or "jupyter")
- `key` (str, optional): Streamlit component key for state management

### Methods

- `set(data: pd.DataFrame)`: Set chart data
- `set_chart_type(chart_type: str)`: Set chart type
- `show()`: Display the chart

## Data Format

### Candlestick Data
```python
pd.DataFrame({
    'time': pd.Timestamp,  # DateTime index
    'open': float,         # Opening price
    'high': float,         # High price
    'low': float,          # Low price
    'close': float,        # Closing price
})
```

### Line/Area/Histogram Data
```python
pd.DataFrame({
    'time': pd.Timestamp,  # DateTime index
    'value': float,        # Data value
})
```

## Examples

See the `examples/` directory for complete working examples:

- `dual_environment_chart_example.py`: Streamlit example
- `jupyter_chart_example.ipynb`: Jupyter notebook example

## Technical Details

### Streamlit Implementation
Uses Streamlit's component framework with a React frontend that integrates with the lightweight-charts library.

### Jupyter Implementation
Generates HTML with embedded JavaScript that loads the lightweight-charts library from CDN and creates the chart inline.

### Environment Detection
The environment must be explicitly specified during chart creation. This ensures predictable behavior and avoids issues with automatic detection.

## Troubleshooting

### Import Errors
- For Streamlit: Ensure `streamlit` is installed
- For Jupyter: Ensure `ipython` and `jupyter` are installed

### Chart Not Displaying
- Check that the environment parameter matches your actual environment
- Verify your data format matches the expected schema
- Ensure JavaScript is enabled in your browser/notebook

### Performance
- For large datasets (>10k points), consider data sampling
- Use appropriate chart types for your data size
