"""
This module provides a web-compatible version of lightweight charts that works
in both Streamlit applications and Jupyter notebooks without requiring
pywebview or desktop dependencies.
"""

import os
import json
import uuid
from typing import Any, Dict, Optional

import pandas as pd
from lightweight_charts.util import js_data

# Try to import Streamlit components, but don't fail if not available
try:
    import streamlit as st
    import streamlit.components.v1 as components

    STREAMLIT_AVAILABLE = True

    parent_dir = os.path.dirname(os.path.abspath(__file__))
    build_dir = os.path.join(parent_dir, "frontend/build")
    _component_func = components.declare_component("terrafin_lightweight_chart", path=build_dir)
except ImportError:
    STREAMLIT_AVAILABLE = False
    st = None
    components = None
    _component_func = None

# Try to import Jupyter display components
try:
    from IPython.display import HTML, display

    JUPYTER_AVAILABLE = True
except ImportError:
    JUPYTER_AVAILABLE = False
    HTML = None
    display = None


class TerraFinChart:
    """
    A dual-environment lightweight charts implementation for TerraFin.

    This class provides an easy-to-use interface for creating interactive
    financial charts in both Streamlit applications and Jupyter notebooks.
    """

    def __init__(
        self,
        width: int = 800,
        height: int = 600,
        theme: str = "dark",
        environment: str = "streamlit",
        key: Optional[str] = None,
    ):
        """
        Initialize a TerraFinChart.

        Args:
            width: Chart width in pixels
            height: Chart height in pixels
            theme: Chart theme ("dark" or "light")
            environment: Target environment ("streamlit" or "jupyter")
            key: Streamlit component key for state management (Streamlit only)
        """
        self.width = width
        self.height = height
        self.theme = theme
        self.environment = environment
        self.key = key or f"terrafin_chart_{id(self)}"
        self.data = None
        self.chart_type = "candlestick"
        self.options = {}
        self.chart_id = f"chart_{uuid.uuid4().hex[:8]}"

    def set(self, data: pd.DataFrame):
        """
        Set the data for the chart.

        Args:
            data: DataFrame with time series data
        """
        if not pd.api.types.is_datetime64_any_dtype(data["time"]):
            data["time"] = pd.to_datetime(data["time"])
        data["time"] = data["time"].astype("int64") // 10**9

        self.data = js_data(data)
        return self

    def show(self) -> Optional[Dict[str, Any]]:
        """
        Display the chart in the specified environment.

        Returns:
            Component value containing any user interactions (Streamlit only)
        """
        if self.data is None:
            error_msg = "No data set. Please call .set() with your data first."
            if self.environment == "streamlit" and STREAMLIT_AVAILABLE:
                st.error(error_msg)
            else:
                print(error_msg)
            return None

        if self.environment == "streamlit":
            return self._show_streamlit()
        elif self.environment == "jupyter":
            return self._show_jupyter()
        else:
            raise ValueError(f"Unsupported environment: {self.environment}")

    def _show_streamlit(self) -> Optional[Dict[str, Any]]:
        """Display the chart in Streamlit."""
        if not STREAMLIT_AVAILABLE:
            raise ImportError("Streamlit is not available. Please install streamlit.")

        if _component_func is None:
            raise RuntimeError("Streamlit component is not properly initialized.")

        # Render the Streamlit component
        component_value = _component_func(
            data=self.data,
            chart_type=self.chart_type,
            width=self.width,
            height=self.height,
            theme=self.theme,
            options=self.options,
            key=self.key,
            default=None,
        )

        return component_value

    def _show_jupyter(self) -> None:
        """Display the chart in Jupyter notebook."""
        if not JUPYTER_AVAILABLE:
            raise ImportError("IPython is not available. Please install ipython or jupyter.")

        # Generate HTML for the chart
        html_content = self._generate_jupyter_html()

        # Display in Jupyter
        display(HTML(html_content))

    def _generate_jupyter_html(self) -> str:
        """Generate HTML content for Jupyter notebook display."""
        # Convert data to JSON
        data_json = json.dumps(self.data)

        # Theme colors
        bg_color = "#1e1e1e" if self.theme == "dark" else "#ffffff"
        text_color = "#d1d4dc" if self.theme == "dark" else "#191919"
        grid_color = "#2B2B43" if self.theme == "dark" else "#e1e3e6"

        # Chart type specific options
        series_options = self._get_series_options_json()

        html_template = f"""
        <div id="{self.chart_id}" style="width: {self.width}px; height: {self.height}px;"></div>
        <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
        <script>
        (function() {{
            const chartContainer = document.getElementById('{self.chart_id}');
            if (!chartContainer) return;

            const chart = LightweightCharts.createChart(chartContainer, {{
                width: {self.width},
                height: {self.height},
                layout: {{
                    background: {{
                        type: LightweightCharts.ColorType.Solid,
                        color: '{bg_color}',
                    }},
                    textColor: '{text_color}',
                }},
                grid: {{
                    vertLines: {{
                        color: '{grid_color}',
                    }},
                    horzLines: {{
                        color: '{grid_color}',
                    }},
                }},
                crosshair: {{
                    mode: LightweightCharts.CrosshairMode.Normal,
                }},
                rightPriceScale: {{
                    borderColor: '{grid_color}',
                }},
                timeScale: {{
                    borderColor: '{grid_color}',
                    timeVisible: true,
                    secondsVisible: false,
                }},
                ...{json.dumps(self.options)}
            }});

            const series = chart.addSeries(LightweightCharts.{self._get_series_type()}, {series_options});
            series.setData({data_json});
            chart.timeScale().fitContent();

            // Handle resize
            const resizeObserver = new ResizeObserver(() => {{
                chart.applyOptions({{
                    width: chartContainer.clientWidth,
                    height: {self.height},
                }});
            }});
            resizeObserver.observe(chartContainer);
        }})();
        </script>
        """

        return html_template

    def _get_series_type(self) -> str:
        """Get the JavaScript series type name for lightweight-charts."""
        type_mapping = {
            "candlestick": "CandlestickSeries",
            "line": "LineSeries",
            "area": "AreaSeries",
            "histogram": "HistogramSeries",
        }
        return type_mapping.get(self.chart_type, "LineSeries")

    def _get_series_options_json(self) -> str:
        """Get series-specific options as JSON string."""
        options = {}

        if self.chart_type == "candlestick":
            options = {
                "upColor": "#26a69a",
                "downColor": "#ef5350",
                "borderVisible": False,
                "wickUpColor": "#26a69a",
                "wickDownColor": "#ef5350",
            }
        elif self.chart_type == "line":
            options = {
                "color": "#2196F3",
                "lineWidth": 2,
            }
        elif self.chart_type == "area":
            options = {
                "topColor": "rgba(33, 150, 243, 0.56)",
                "bottomColor": "rgba(33, 150, 243, 0.04)",
                "lineColor": "rgba(33, 150, 243, 1)",
                "lineWidth": 2,
            }
        elif self.chart_type == "histogram":
            options = {
                "color": "#26a69a",
                "priceFormat": {
                    "type": "volume",
                },
                "priceScaleId": "",
            }

        return json.dumps(options)
