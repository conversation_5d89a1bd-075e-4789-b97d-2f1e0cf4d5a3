"""
This module provides a web-compatible version of lightweight charts that works
in both Streamlit applications and Jupyter notebooks without requiring
pywebview or desktop dependencies.
"""

import os
import json
import uuid
from typing import Any, Dict, Optional

import pandas as pd
from lightweight_charts.util import js_data

# Try to import Streamlit components, but don't fail if not available
try:
    import streamlit as st
    import streamlit.components.v1 as components

    STREAMLIT_AVAILABLE = True

    parent_dir = os.path.dirname(os.path.abspath(__file__))
    build_dir = os.path.join(parent_dir, "frontend/build")
    _component_func = components.declare_component("terrafin_lightweight_chart", path=build_dir)
except ImportError:
    STREAMLIT_AVAILABLE = False
    st = None
    components = None
    _component_func = None

# Try to import Jupyter display components
try:
    from IPython.display import HTML, display

    JUPYTER_AVAILABLE = True
except ImportError:
    JUPYTER_AVAILABLE = False
    HTML = None
    display = None


def _detect_environment():
    """Detect if we're running in Streamlit, Jupyter, or standalone."""
    if STREAMLIT_AVAILABLE:
        try:
            # Check if we're in a Streamlit context
            import streamlit.runtime.scriptrunner as sr

            if sr.get_script_run_ctx() is not None:
                return "streamlit"
        except:
            pass

    if JUPYTER_AVAILABLE:
        try:
            # Check if we're in a Jupyter context
            from IPython import get_ipython

            if get_ipython() is not None:
                return "jupyter"
        except:
            pass

    return "standalone"


class TerraFinChart:
    """
    A Streamlit-compatible lightweight charts implementation for TerraFin.

    This class provides an easy-to-use interface for creating interactive
    financial charts in Streamlit applications.
    """

    def __init__(self, width: int = 800, height: int = 600, theme: str = "dark", key: Optional[str] = None):
        """
        Initialize a TerraFinChart.

        Args:
            width: Chart width in pixels
            height: Chart height in pixels
            theme: Chart theme ("dark" or "light")
            key: Streamlit component key for state management
        """
        self.width = width
        self.height = height
        self.theme = theme
        self.key = key or f"terrafin_chart_{id(self)}"
        self.data = None
        self.chart_type = "candlestick"
        self.options = {}

    def set(self, data: pd.DataFrame):
        """
        Set the data for the chart.

        Args:
            data: DataFrame with time series data
        """
        if not pd.api.types.is_datetime64_any_dtype(data["time"]):
            data["time"] = pd.to_datetime(data["time"])
        data["time"] = data["time"].astype("int64") // 10**9

        self.data = js_data(data)
        return self

    def show(self) -> Optional[Dict[str, Any]]:
        """
        Display the chart in Streamlit.

        Returns:
            Component value containing any user interactions
        """
        if self.data is None:
            st.error("No data set. Please call .set() with your data first.")
            return None

        # Render the Streamlit component
        component_value = _component_func(
            data=self.data,
            chart_type=self.chart_type,
            width=self.width,
            height=self.height,
            theme=self.theme,
            options=self.options,
            key=self.key,
            default=None,
        )

        return component_value
