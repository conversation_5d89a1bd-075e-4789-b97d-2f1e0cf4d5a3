"""
This module provides a web-compatible version of lightweight charts that works
in Streamlit applications without requiring pywebview or desktop dependencies.
"""

import os
from typing import Any, Dict, Optional

import pandas as pd
import streamlit as st
import streamlit.components.v1 as components
from lightweight_charts.util import js_data


parent_dir = os.path.dirname(os.path.abspath(__file__))
build_dir = os.path.join(parent_dir, "frontend/build")
_component_func = components.declare_component("terrafin_lightweight_chart", path=build_dir)


class TerraFinChart:
    """
    A Streamlit-compatible lightweight charts implementation for TerraFin.

    This class provides an easy-to-use interface for creating interactive
    financial charts in Streamlit applications.
    """

    def __init__(self, width: int = 800, height: int = 600, theme: str = "dark", key: Optional[str] = None):
        """
        Initialize a TerraFinChart.

        Args:
            width: Chart width in pixels
            height: Chart height in pixels
            theme: Chart theme ("dark" or "light")
            key: Streamlit component key for state management
        """
        self.width = width
        self.height = height
        self.theme = theme
        self.key = key or f"terrafin_chart_{id(self)}"
        self.data = None
        self.chart_type = "candlestick"
        self.options = {}

    def set(self, data: pd.DataFrame):
        """
        Set the data for the chart.

        Args:
            data: DataFrame with time series data
        """
        if not pd.api.types.is_datetime64_any_dtype(data["time"]):
            data["time"] = pd.to_datetime(data["time"])
        data["time"] = data["time"].astype("int64") // 10**9

        self.data = js_data(data)
        return self

    def show(self) -> Optional[Dict[str, Any]]:
        """
        Display the chart in Streamlit.

        Returns:
            Component value containing any user interactions
        """
        if self.data is None:
            st.error("No data set. Please call .set() with your data first.")
            return None

        # Render the Streamlit component
        component_value = _component_func(
            data=self.data,
            chart_type=self.chart_type,
            width=self.width,
            height=self.height,
            theme=self.theme,
            options=self.options,
            key=self.key,
            default=None,
        )

        return component_value
